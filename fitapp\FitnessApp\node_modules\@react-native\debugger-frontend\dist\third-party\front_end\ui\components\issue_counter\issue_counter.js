import*as e from"../../../core/common/common.js";import*as s from"../../../core/i18n/i18n.js";import*as i from"../../../models/issues_manager/issues_manager.js";import*as t from"../../lit-html/lit-html.js";import*as o from"../icon_button/icon_button.js";import*as n from"../render_coordinator/render_coordinator.js";import*as r from"../../visual_logging/visual_logging.js";const a=new CSSStyleSheet;a.replaceSync(":host{white-space:normal;display:inline-block}\n/*# sourceURL=issueCounter.css */\n");const l={pageErrors:"{issueCount, plural, =1 {# page error} other {# page errors}}",breakingChanges:"{issueCount, plural, =1 {# breaking change} other {# breaking changes}}",possibleImprovements:"{issueCount, plural, =1 {# possible improvement} other {# possible improvements}}"},c=s.i18n.registerUIStrings("ui/components/issue_counter/IssueCounter.ts",l),u=s.i18n.getLocalizedString.bind(void 0,c);function h(e){switch(e){case"PageError":return{iconName:"issue-cross-filled",color:"var(--icon-error)",width:"20px",height:"20px"};case"BreakingChange":return{iconName:"issue-exclamation-filled",color:"var(--icon-warning)",width:"20px",height:"20px"};case"Improvement":return{iconName:"issue-text-filled",color:"var(--icon-info)",width:"20px",height:"20px"}}}function d({iconName:e,color:s,width:i,height:t},o){return o?{iconName:e,iconColor:s,iconWidth:o,iconHeight:o}:{iconName:e,iconColor:s,iconWidth:i,iconHeight:t}}const m=new Intl.ListFormat(navigator.language,{type:"unit",style:"short"});class p extends HTMLElement{static litTagName=t.literal`devtools-issue-counter`;#e=this.attachShadow({mode:"open"});#s=void 0;#i=void 0;#t="";#o;#n=[0,0,0];#r="OmitEmpty";#a=void 0;#l=void 0;#c;#u=!1;scheduleUpdate(){this.#o?this.#o.schedule((async()=>this.#h())):this.#h()}connectedCallback(){this.#e.adoptedStyleSheets=[a]}set data(s){this.#s=s.clickHandler,this.#t=s.leadingText??"",this.#i=s.tooltipCallback,this.#r=s.displayMode??"OmitEmpty",this.#l=s.accessibleName,this.#c=s.throttlerTimeout,this.#u=Boolean(s.compact),this.#a!==s.issuesManager&&(this.#a?.removeEventListener("IssuesCountUpdated",this.scheduleUpdate,this),this.#a=s.issuesManager,this.#a.addEventListener("IssuesCountUpdated",this.scheduleUpdate,this)),0!==s.throttlerTimeout?this.#o=new e.Throttler.Throttler(s.throttlerTimeout??100):this.#o=void 0,this.scheduleUpdate()}get data(){return{clickHandler:this.#s,leadingText:this.#t,tooltipCallback:this.#i,displayMode:this.#r,accessibleName:this.#l,throttlerTimeout:this.#c,compact:this.#u,issuesManager:this.#a}}#h(){if(!this.#a)return;this.#n=[this.#a.numberOfIssues("PageError"),this.#a.numberOfIssues("BreakingChange"),this.#a.numberOfIssues("Improvement")];const e=["PageError","BreakingChange","Improvement"][this.#n.findIndex((e=>e>0))??2],s=(s,i)=>{switch(this.#r){case"OmitEmpty":return i>0?`${i}`:void 0;case"ShowAlways":return`${i}`;case"OnlyMostImportant":return s===e?`${i}`:void 0}},i="2ex",o={groups:[{...d(h("PageError"),i),text:s("PageError",this.#n[0])},{...d(h("BreakingChange"),i),text:s("BreakingChange",this.#n[1])},{...d(h("Improvement"),i),text:s("Improvement",this.#n[2])}],clickHandler:this.#s,leadingText:this.#t,accessibleName:this.#l,compact:this.#u};t.render(t.html`
        <icon-button .data=${o} .accessibleName=${this.#l}></icon-button>
        `,this.#e,{host:this}),this.#i?.()}}customElements.define("devtools-issue-counter",p);var g=Object.freeze({__proto__:null,getIssueKindIconData:h,getIssueCountsEnumeration:function(e,s=!0){const i=[e.numberOfIssues("PageError"),e.numberOfIssues("BreakingChange"),e.numberOfIssues("Improvement")],t=[u(l.pageErrors,{issueCount:i[0]}),u(l.breakingChanges,{issueCount:i[1]}),u(l.possibleImprovements,{issueCount:i[2]})];return m.format(t.filter(((e,t)=>!s||i[t]>0)))},IssueCounter:p});const v=new CSSStyleSheet;v.replaceSync(':host{display:inline-block;white-space:nowrap;color:inherit;font-size:inherit;font-family:inherit}:host([hidden]){display:none}button{border:none;background:transparent;margin:0;padding:0;&.link{cursor:pointer;& > span{color:var(--sys-color-primary)}}}devtools-icon{width:16px;height:16px;vertical-align:middle;&[name="issue-cross-filled"]{color:var(--icon-error)}&[name="issue-exclamation-filled"]{color:var(--icon-warning)}&[name="issue-text-filled"]{color:var(--icon-info)}}@media (forced-colors: active){devtools-icon{color:ButtonText}}\n/*# sourceURL=issueLinkIcon.css */\n');const I={clickToShowIssue:"Click to show issue in the issues tab",clickToShowIssueWithTitle:"Click to open the issue tab and show issue: {title}",issueUnavailable:"Issue unavailable at this time"},k=s.i18n.registerUIStrings("ui/components/issue_counter/IssueLinkIcon.ts",I),b=s.i18n.getLocalizedString.bind(void 0,k),f=n.RenderCoordinator.RenderCoordinator.instance();class C extends HTMLElement{static litTagName=t.literal`devtools-issue-link-icon`;#e=this.attachShadow({mode:"open"});#d;#m=null;#p;#g;#v;#I=e.Revealer.reveal;set data(e){if(this.#d=e.issue,this.#p=e.issueId,this.#g=e.issueResolver,!this.#d){if(!this.#p)throw new Error("Either `issue` or `issueId` must be provided");if(!this.#g)throw new Error("An `IssueResolver` must be provided if an `issueId` is provided.")}this.#v=e.additionalOnClickAction,e.revealOverride&&(this.#I=e.revealOverride),this.#k(),this.#h()}async#k(){if(!this.#d&&this.#p)try{this.#d=await(this.#g?.waitFor(this.#p))}catch{this.#d=null}const e=this.#d?.getDescription();if(e){const s=await i.MarkdownIssueDescription.getIssueTitleFromMarkdownDescription(e);s&&(this.#m=s)}await this.#h()}connectedCallback(){this.#e.adoptedStyleSheets=[v]}get data(){return{issue:this.#d,issueId:this.#p,issueResolver:this.#g,additionalOnClickAction:this.#v,revealOverride:this.#I!==e.Revealer.reveal?this.#I:void 0}}handleClick(e){0===e.button&&(this.#d&&this.#I(this.#d),this.#v?.(),e.consume())}#b(){return this.#m?b(I.clickToShowIssueWithTitle,{title:this.#m}):this.#d?b(I.clickToShowIssue):b(I.issueUnavailable)}#f(){if(!this.#d)return"issue-questionmark-filled";const{iconName:e}=h(this.#d.getKind());return e}#h(){return f.write((()=>{t.render(t.html`
      <button class=${t.Directives.classMap({link:Boolean(this.#d)})}
              title=${this.#b()}
              jslog=${r.link("issue").track({click:!0})}
              @click=${this.handleClick}>
        <${o.Icon.Icon.litTagName} name=${this.#f()}></${o.Icon.Icon.litTagName}>
      </span>`,this.#e,{host:this})}))}}customElements.define("devtools-issue-link-icon",C);var w=Object.freeze({__proto__:null,extractShortPath:e=>(/[^/]+$/.exec(e)||/[^/]+\/$/.exec(e)||[""])[0],IssueLinkIcon:C});export{g as IssueCounter,w as IssueLinkIcon};
