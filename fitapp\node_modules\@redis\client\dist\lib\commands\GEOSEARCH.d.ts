import { RedisCommandArgument, RedisCommandArguments } from '.';
import { GeoSearchFrom, GeoSearchBy, GeoSearchOptions } from './generic-transformers';
export declare const FIRST_KEY_INDEX = 1;
export declare const IS_READ_ONLY = true;
export declare function transformArguments(key: RedisCommandArgument, from: GeoSearchFrom, by: GeoSearchBy, options?: GeoSearchOptions): RedisCommandArguments;
export declare function transformReply(): Array<RedisCommandArgument>;
