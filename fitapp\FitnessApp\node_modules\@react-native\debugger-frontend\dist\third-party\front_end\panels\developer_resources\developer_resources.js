import*as e from"../../core/i18n/i18n.js";import*as t from"../../core/platform/platform.js";import*as i from"../../core/sdk/sdk.js";import*as s from"../../ui/legacy/legacy.js";import*as r from"../../ui/visual_logging/visual_logging.js";import*as o from"../../core/host/host.js";import*as l from"../../models/text_utils/text_utils.js";import*as a from"../../ui/legacy/components/data_grid/data_grid.js";const n=new CSSStyleSheet;n.replaceSync(".data-grid{border:none}.data-grid td .url-outer{width:100%;display:inline-flex;justify-content:flex-start}.data-grid td .url-outer .filter-highlight{font-weight:bold}.data-grid td .url-prefix{overflow-x:hidden;text-overflow:ellipsis}.data-grid td .url-suffix{flex:none}.data-grid td.error-message .filter-highlight{font-weight:bold}\n/*# sourceURL=developerResourcesListView.css */\n");const d={status:"Status",url:"URL",initiator:"Initiator",totalBytes:"Total Bytes",error:"Error",developerResources:"Developer resources",copyUrl:"Copy URL",copyInitiatorUrl:"Copy initiator URL",pending:"pending",success:"success",failure:"failure",sBytes:"{n, plural, =1 {# byte} other {# bytes}}"},h=e.i18n.registerUIStrings("panels/developer_resources/DeveloperResourcesListView.ts",d),c=e.i18n.getLocalizedString.bind(void 0,h);class u extends s.Widget.VBox{nodeForItem;isVisibleFilter;highlightRegExp;dataGrid;constructor(e){super(!0),this.nodeForItem=new Map,this.isVisibleFilter=e,this.highlightRegExp=null;const t=[{id:"status",title:c(d.status),width:"60px",fixedWidth:!0,sortable:!0},{id:"url",title:c(d.url),width:"250px",fixedWidth:!1,sortable:!0},{id:"initiator",title:c(d.initiator),width:"80px",fixedWidth:!1,sortable:!0},{id:"size",title:c(d.totalBytes),width:"80px",fixedWidth:!0,sortable:!0,align:"right"},{id:"error-message",title:c(d.error),width:"200px",fixedWidth:!1,sortable:!0}];this.dataGrid=new a.SortableDataGrid.SortableDataGrid({displayName:c(d.developerResources),columns:t,editCallback:void 0,refreshCallback:void 0,deleteCallback:void 0}),this.dataGrid.setResizeMethod("last"),this.dataGrid.setStriped(!0),this.dataGrid.element.classList.add("flex-auto"),this.dataGrid.addEventListener("SortingChanged",this.sortingChanged,this),this.dataGrid.setRowContextMenuCallback(this.populateContextMenu.bind(this));const i=this.dataGrid.asWidget();i.show(this.contentElement),this.setDefaultFocusedChild(i)}select(e){const t=this.nodeForItem.get(e);t&&t.select()}selectedItem(){return this.dataGrid.selectedNode?this.dataGrid.selectedNode.item:null}populateContextMenu(e,t){const i=t.item;e.clipboardSection().appendItem(c(d.copyUrl),(()=>{o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(i.url)}),{jslogContext:"copy-url"}),i.initiator.initiatorUrl&&e.clipboardSection().appendItem(c(d.copyInitiatorUrl),(()=>{o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(i.initiator.initiatorUrl)}),{jslogContext:"copy-initiator-url"})}update(e){let t=!1;const i=this.dataGrid.rootNode();for(const s of e){let e=this.nodeForItem.get(s);e?this.isVisibleFilter(e.item)&&(t=e.refreshIfNeeded()||t):(e=new g(s),this.nodeForItem.set(s,e),this.isVisibleFilter(e.item)&&(i.appendChild(e),t=!0))}t&&this.sortingChanged()}reset(){this.nodeForItem.clear(),this.dataGrid.rootNode().removeChildren()}updateFilterAndHighlight(e){this.highlightRegExp=e;let t=!1;for(const e of this.nodeForItem.values()){const i=this.isVisibleFilter(e.item),s=Boolean(e.parent);i&&e.setHighlight(this.highlightRegExp),i!==s&&(t=!0,i?this.dataGrid.rootNode().appendChild(e):e.remove())}t&&this.sortingChanged()}sortingChanged(){const e=this.dataGrid.sortColumnId();if(!e)return;const t=g.sortFunctionForColumn(e);t&&this.dataGrid.sortNodes(t,!this.dataGrid.isSortOrderAscending())}wasShown(){super.wasShown(),this.registerCSSFiles([n])}}class g extends a.SortableDataGrid.SortableDataGridNode{item;highlightRegExp;constructor(e){super(),this.item=e,this.highlightRegExp=null}setHighlight(e){this.highlightRegExp!==e&&(this.highlightRegExp=e,this.refresh())}refreshIfNeeded(){return this.refresh(),!0}createCell(e){const r=this.createTD(e);switch(e){case"url":{s.Tooltip.Tooltip.install(r,this.item.url);const t=r.createChild("div","url-outer"),i=t.createChild("div","url-prefix"),o=t.createChild("div","url-suffix"),l=/^(.*)(\/[^/]*)$/.exec(this.item.url);i.textContent=l?l[1]:this.item.url,o.textContent=l?l[2]:"",this.highlightRegExp&&this.highlight(t,this.item.url),this.setCellAccessibleName(this.item.url,r,e);break}case"initiator":{const t=this.item.initiator.initiatorUrl||"";r.textContent=t,s.Tooltip.Tooltip.install(r,t),this.setCellAccessibleName(t,r,e),r.onmouseenter=()=>{const e=this.item.initiator.frameId,t=e?i.FrameManager.FrameManager.instance().getFrame(e):null;t&&t.highlight()},r.onmouseleave=()=>i.OverlayModel.OverlayModel.hideDOMNodeHighlight();break}case"status":null===this.item.success?r.textContent=c(d.pending):r.textContent=this.item.success?c(d.success):c(d.failure);break;case"size":{const i=this.item.size;if(null!==i){r.createChild("span").textContent=t.NumberUtilities.withThousandsSeparator(i);const s=c(d.sBytes,{n:i});this.setCellAccessibleName(s,r,e)}break}case"error-message":r.classList.add("error-message"),this.item.errorMessage&&(r.textContent=this.item.errorMessage,this.highlightRegExp&&this.highlight(r,this.item.errorMessage))}return r}highlight(e,t){if(!this.highlightRegExp)return;const i=this.highlightRegExp.exec(t);if(!i||!i.length)return;const r=new l.TextRange.SourceRange(i.index,i[0].length);s.UIUtils.highlightRangesWithStyleClass(e,[r],"filter-highlight")}static sortFunctionForColumn(e){const t=e=>null===e?-1:Number(e);switch(e){case"url":return(e,t)=>e.item.url.localeCompare(t.item.url);case"status":return(e,i)=>t(e.item.success)-t(i.item.success);case"size":return(e,i)=>t(e.item.size)-t(i.item.size);case"initiator":return(e,t)=>(e.item.initiator.initiatorUrl||"").localeCompare(t.item.initiator.initiatorUrl||"");case"error-message":return(e,t)=>(e.item.errorMessage||"").localeCompare(t.item.errorMessage||"");default:return console.assert(!1,"Unknown sort field: "+e),null}}}const p=new CSSStyleSheet;p.replaceSync(":host{overflow:hidden}.developer-resource-view-toolbar-container{display:flex;border-bottom:1px solid var(--sys-color-divider);flex:0 0 auto}.developer-resource-view-toolbar{display:inline-block;width:100%}.developer-resource-view-toolbar-summary{background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);padding-left:5px;flex:0 0 19px;display:flex;padding-right:5px}.developer-resource-view-toolbar-summary .developer-resource-view-message{padding-top:2px;padding-left:1ex;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.developer-resource-view-results{overflow-y:auto;display:flex;flex:auto}\n/*# sourceURL=developerResourcesView.css */\n");const m={filterByText:"Filter by URL and error",loadHttpsDeveloperResources:"Load `HTTP(S)` developer resources through the website you inspect, not through DevTools",enableLoadingThroughTarget:"Load through website",resourcesCurrentlyLoading:"{PH1} resources, {PH2} currently loading",resources:"{n, plural, =1 {# resource} other {# resources}}"},x=e.i18n.registerUIStrings("panels/developer_resources/DeveloperResourcesView.ts",m),v=e.i18n.getLocalizedString.bind(void 0,x);class w extends s.ThrottledWidget.ThrottledWidget{textFilterRegExp;filterInput;coverageResultsElement;listView;statusToolbarElement;statusMessageElement;loader;constructor(){super(!0),this.element.setAttribute("jslog",`${r.panel("developer-resources").track({resize:!0})}`);const e=this.contentElement.createChild("div","developer-resource-view-toolbar-container");e.setAttribute("jslog",`${r.toolbar()}`);const t=new s.Toolbar.Toolbar("developer-resource-view-toolbar",e);this.textFilterRegExp=null,this.filterInput=new s.Toolbar.ToolbarFilter(v(m.filterByText),1),this.filterInput.addEventListener("TextChanged",this.onFilterChanged,this),t.appendToolbarItem(this.filterInput);const o=i.PageResourceLoader.getLoadThroughTargetSetting(),l=new s.Toolbar.ToolbarSettingCheckbox(o,v(m.loadHttpsDeveloperResources),v(m.enableLoadingThroughTarget));t.appendToolbarItem(l),this.coverageResultsElement=this.contentElement.createChild("div","developer-resource-view-results"),this.listView=new u(this.isVisible.bind(this)),this.listView.show(this.coverageResultsElement),this.statusToolbarElement=this.contentElement.createChild("div","developer-resource-view-toolbar-summary"),this.statusMessageElement=this.statusToolbarElement.createChild("div","developer-resource-view-message"),this.loader=i.PageResourceLoader.PageResourceLoader.instance(),this.loader.addEventListener("Update",this.update,this),this.update()}async doUpdate(){const e=this.listView.selectedItem();this.listView.reset(),this.listView.update(this.loader.getScopedResourcesLoaded().values()),e&&this.listView.select(e),this.updateStats()}async select(e){await this.lastUpdatePromise,this.listView.select(e)}async selectedItem(){return await this.lastUpdatePromise,this.listView.selectedItem()}updateStats(){const{loading:e,resources:t}=this.loader.getScopedNumberOfResources();this.statusMessageElement.textContent=e>0?v(m.resourcesCurrentlyLoading,{PH1:t,PH2:e}):v(m.resources,{n:t})}isVisible(e){return!this.textFilterRegExp||this.textFilterRegExp.test(e.url)||this.textFilterRegExp.test(e.errorMessage||"")}onFilterChanged(){if(!this.listView)return;const e=this.filterInput.value();this.textFilterRegExp=e?t.StringUtilities.createPlainTextSearchRegex(e,"i"):null,this.listView.updateFilterAndHighlight(this.textFilterRegExp),this.updateStats()}wasShown(){super.wasShown(),this.registerCSSFiles([p])}}var f=Object.freeze({__proto__:null,DeveloperResourcesRevealer:class{async reveal(e){const t=i.PageResourceLoader.PageResourceLoader.instance().getResourcesLoaded().get(e.key);if(t){await s.ViewManager.ViewManager.instance().showView("developer-resources");return(await s.ViewManager.ViewManager.instance().view("developer-resources").widget()).select(t)}}},DeveloperResourcesView:w});export{f as DeveloperResourcesView};
