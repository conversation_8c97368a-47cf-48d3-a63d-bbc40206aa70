{"version": 3, "sources": ["../../src/android/AndroidParser.ts"], "names": ["AndroidParser", "splitMessages", "raw", "messages", "data", "toString", "match", "timeRegex", "timeHeader", "slice", "index", "length", "nextMatch", "body", "push", "parseMessages", "map", "rawMessage", "timeMatch", "Error", "headerMatch", "headerRegex", "priority", "tag", "pid", "platform", "date", "set", "parseInt", "trim", "Priority", "fromLetter", "reduce", "acc", "entry", "isSame"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;AAEe,MAAMA,aAAN,CAAuC;AAIpDC,EAAAA,aAAa,CAACC,GAAD,EAAwB;AACnC,UAAMC,QAAkB,GAAG,EAA3B;AACA,QAAIC,IAAI,GAAGF,GAAG,CAACG,QAAJ,EAAX;AACA,QAAIC,KAAK,GAAGF,IAAI,CAACE,KAAL,CAAWN,aAAa,CAACO,SAAzB,CAAZ;;AACA,WAAOD,KAAP,EAAc;AACZ,YAAME,UAAU,GAAGF,KAAK,CAAC,CAAD,CAAxB;AACAF,MAAAA,IAAI,GAAGA,IAAI,CAACK,KAAL,CAAW,CAACH,KAAK,CAACI,KAAN,IAAe,CAAhB,IAAqBF,UAAU,CAACG,MAA3C,CAAP;AACA,YAAMC,SAAS,GAAGR,IAAI,CAACE,KAAL,CAAWN,aAAa,CAACO,SAAzB,CAAlB;AACA,YAAMM,IAAI,GAAGD,SAAS,GAAGR,IAAI,CAACK,KAAL,CAAW,CAAX,EAAcG,SAAS,CAACF,KAAxB,CAAH,GAAoCN,IAA1D;AACAD,MAAAA,QAAQ,CAACW,IAAT,CAAe,GAAEN,UAAW,IAAGK,IAAK,EAApC;AACAP,MAAAA,KAAK,GAAGM,SAAR;AACD;;AACD,WAAOT,QAAP;AACD;;AAEDY,EAAAA,aAAa,CAACZ,QAAD,EAA8B;AACzC,WAAOA,QAAQ,CACZa,GADI,CAEFC,UAAD,IAA+B;AAC7B,YAAMC,SAAS,GAAGD,UAAU,CAACX,KAAX,CAAiBN,aAAa,CAACO,SAA/B,CAAlB;;AACA,UAAI,CAACW,SAAL,EAAgB;AACd,cAAM,IAAIC,KAAJ,CACH,0CAAyCF,UAAW,EADjD,CAAN;AAGD;;AAED,YAAMG,WAAW,GAAGH,UAAU,CAC3BR,KADiB,CACXS,SAAS,CAAC,CAAD,CAAT,CAAaP,MADF,EAEjBL,KAFiB,CAEXN,aAAa,CAACqB,WAFH,KAEmB,CAAC,EAAD,EAAK,GAAL,EAAU,SAAV,EAAqB,IAArB,CAFvC;AAIA,YAAM,GAAGC,QAAH,EAAaC,GAAb,EAAkBC,GAAlB,IAAyBJ,WAA/B;AACA,aAAO;AACLK,QAAAA,QAAQ,EAAE,SADL;AAELC,QAAAA,IAAI,EAAE,sBACHC,GADG,CACC,OADD,EACUC,QAAQ,CAACV,SAAS,CAAC,CAAD,CAAV,EAAe,EAAf,CADlB,EAEHS,GAFG,CAEC,KAFD,EAEQC,QAAQ,CAACV,SAAS,CAAC,CAAD,CAAV,EAAe,EAAf,CAFhB,EAGHS,GAHG,CAGC,MAHD,EAGSC,QAAQ,CAACV,SAAS,CAAC,CAAD,CAAV,EAAe,EAAf,CAHjB,EAIHS,GAJG,CAIC,QAJD,EAIWC,QAAQ,CAACV,SAAS,CAAC,CAAD,CAAV,EAAe,EAAf,CAJnB,EAKHS,GALG,CAKC,QALD,EAKWC,QAAQ,CAACV,SAAS,CAAC,CAAD,CAAV,EAAe,EAAf,CALnB,EAMHS,GANG,CAMC,aAND,EAMgB,CANhB,CAFD;AASLH,QAAAA,GAAG,EAAEI,QAAQ,CAACJ,GAAG,CAACK,IAAJ,EAAD,EAAa,EAAb,CAAR,IAA4B,CAT5B;AAULP,QAAAA,QAAQ,EAAEQ,oBAASC,UAAT,CAAoBT,QAApB,CAVL;AAWLC,QAAAA,GAAG,EAAEA,GAAG,CAACM,IAAJ,MAAc,SAXd;AAYL1B,QAAAA,QAAQ,EAAE,CACRc,UAAU,CACPR,KADH,CACSS,SAAS,CAAC,CAAD,CAAT,CAAaP,MAAb,GAAsBS,WAAW,CAAC,CAAD,CAAX,CAAeT,MAD9C,EAEGkB,IAFH,EADQ;AAZL,OAAP;AAkBD,KAjCE,EAmCJG,MAnCI,CAmCG,CAACC,GAAD,EAAeC,KAAf,KAAgC;AACtC,UACED,GAAG,CAACtB,MAAJ,GAAa,CAAb,IACAsB,GAAG,CAACA,GAAG,CAACtB,MAAJ,GAAa,CAAd,CAAH,CAAoBe,IAApB,CAAyBS,MAAzB,CAAgCD,KAAK,CAACR,IAAtC,CADA,IAEAO,GAAG,CAACA,GAAG,CAACtB,MAAJ,GAAa,CAAd,CAAH,CAAoBY,GAApB,KAA4BW,KAAK,CAACX,GAFlC,IAGAU,GAAG,CAACA,GAAG,CAACtB,MAAJ,GAAa,CAAd,CAAH,CAAoBa,GAApB,KAA4BU,KAAK,CAACV,GAHlC,IAIAS,GAAG,CAACA,GAAG,CAACtB,MAAJ,GAAa,CAAd,CAAH,CAAoBW,QAApB,KAAiCY,KAAK,CAACZ,QALzC,EAME;AACAW,QAAAA,GAAG,CAACA,GAAG,CAACtB,MAAJ,GAAa,CAAd,CAAH,CAAoBR,QAApB,CAA6BW,IAA7B,CAAkC,GAAGoB,KAAK,CAAC/B,QAA3C;AACA,eAAO8B,GAAP;AACD;;AAED,aAAO,CAAC,GAAGA,GAAJ,EAASC,KAAT,CAAP;AACD,KAhDI,EAgDF,EAhDE,CAAP;AAiDD;;AArEmD;;;;gBAAjClC,a,eACQ,gD;;gBADRA,a,iBAEU,8B", "sourcesContent": ["import DayJS from 'dayjs';\nimport { IParser, Entry } from '../types';\nimport { Priority } from './constants';\n\nexport default class AndroidParser implements IParser {\n  static timeRegex: RegExp = /(\\d{2})-(\\d{2}) (\\d{2}):(\\d{2}):(\\d{2}).\\d{3}/m;\n  static headerRegex: RegExp = /^\\s*(\\w)\\/(.+)\\(([\\s\\d]+)\\):/;\n\n  splitMessages(raw: string): string[] {\n    const messages: string[] = [];\n    let data = raw.toString();\n    let match = data.match(AndroidParser.timeRegex);\n    while (match) {\n      const timeHeader = match[0];\n      data = data.slice((match.index || 0) + timeHeader.length);\n      const nextMatch = data.match(AndroidParser.timeRegex);\n      const body = nextMatch ? data.slice(0, nextMatch.index) : data;\n      messages.push(`${timeHeader} ${body}`);\n      match = nextMatch;\n    }\n    return messages;\n  }\n\n  parseMessages(messages: string[]): Entry[] {\n    return messages\n      .map(\n        (rawMessage: string): Entry => {\n          const timeMatch = rawMessage.match(AndroidParser.timeRegex);\n          if (!timeMatch) {\n            throw new Error(\n              `Time regex was not matched in message: ${rawMessage}`\n            );\n          }\n\n          const headerMatch = rawMessage\n            .slice(timeMatch[0].length)\n            .match(AndroidParser.headerRegex) || ['', 'U', 'unknown', '-1'];\n\n          const [, priority, tag, pid] = headerMatch;\n          return {\n            platform: 'android',\n            date: DayJS()\n              .set('month', parseInt(timeMatch[1], 10))\n              .set('day', parseInt(timeMatch[2], 10))\n              .set('hour', parseInt(timeMatch[3], 10))\n              .set('minute', parseInt(timeMatch[4], 10))\n              .set('second', parseInt(timeMatch[5], 10))\n              .set('millisecond', 0),\n            pid: parseInt(pid.trim(), 10) || 0,\n            priority: Priority.fromLetter(priority),\n            tag: tag.trim() || 'unknown',\n            messages: [\n              rawMessage\n                .slice(timeMatch[0].length + headerMatch[0].length)\n                .trim(),\n            ],\n          };\n        }\n      )\n      .reduce((acc: Entry[], entry: Entry) => {\n        if (\n          acc.length > 0 &&\n          acc[acc.length - 1].date.isSame(entry.date) &&\n          acc[acc.length - 1].tag === entry.tag &&\n          acc[acc.length - 1].pid === entry.pid &&\n          acc[acc.length - 1].priority === entry.priority\n        ) {\n          acc[acc.length - 1].messages.push(...entry.messages);\n          return acc;\n        }\n\n        return [...acc, entry];\n      }, []);\n  }\n}\n"], "file": "AndroidParser.js"}