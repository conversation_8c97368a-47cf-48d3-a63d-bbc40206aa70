import { z } from 'zod';

// Kullanıcı profil şemaları
export const UserProfileSchema = z.object({
  name: z.string().min(1, 'İsim gereklidir'),
  age: z.number().min(1, 'Yaş 1-120 arasında olmalıdır').max(120, 'Yaş 1-120 arasında olmalıdır'),
  gender: z.enum(['erkek', 'kadın'], {
    errorMap: () => ({ message: 'Cinsiyet erkek veya kadın olmalıdır' })
  }),
  height: z.number().min(50, 'Boy 50-250 cm arasında olmalıdır').max(250, 'Boy 50-250 cm arasında olmalıdır'),
  weight: z.number().min(20, 'Kilo 20-300 kg arasında olmalıdır').max(300, 'Kilo 20-300 kg arasında olmalıdır'),
  activityLevel: z.enum(['sedanter', 'hafif_aktif', 'orta_aktif', 'çok_aktif', 'aşırı_aktif'], {
    errorMap: () => ({ message: 'Aktivite seviyesi geçerli değil' })
  }),
  healthConditions: z.array(z.string()).optional(),
  allergies: z.array(z.string()).optional(),
  fitnessGoals: z.array(z.enum(['kilo_verme', 'kilo_alma', 'kas_kazanma', 'dayanıklılık', 'genel_sağlık'])).optional()
});

export const BMIInputSchema = z.object({
  height: z.number().min(50, 'Boy 50-250 cm arasında olmalıdır').max(250, 'Boy 50-250 cm arasında olmalıdır'),
  weight: z.number().min(20, 'Kilo 20-300 kg arasında olmalıdır').max(300, 'Kilo 20-300 kg arasında olmalıdır')
});

export const NutritionRequestSchema = z.object({
  userProfile: UserProfileSchema,
  bmiCategory: z.enum(['zayıf', 'normal', 'fazla_kilolu', 'obez_1', 'obez_2', 'obez_3']),
  dailyCalorieNeeds: z.number().positive('Günlük kalori ihtiyacı pozitif olmalıdır')
});

export const WorkoutRequestSchema = z.object({
  userProfile: UserProfileSchema,
  bmiCategory: z.enum(['zayıf', 'normal', 'fazla_kilolu', 'obez_1', 'obez_2', 'obez_3']),
  fitnessLevel: z.enum(['başlangıç', 'orta', 'ileri']),
  availableTime: z.number().min(15, 'En az 15 dakika olmalıdır').max(180, 'En fazla 180 dakika olmalıdır'),
  preferredWorkoutType: z.enum(['kardiyovasküler', 'güç_antrenmanı', 'karma', 'yoga', 'pilates']).optional()
});

export type UserProfile = z.infer<typeof UserProfileSchema>;
export type BMIInput = z.infer<typeof BMIInputSchema>;
export type NutritionRequest = z.infer<typeof NutritionRequestSchema>;
export type WorkoutRequest = z.infer<typeof WorkoutRequestSchema>;
