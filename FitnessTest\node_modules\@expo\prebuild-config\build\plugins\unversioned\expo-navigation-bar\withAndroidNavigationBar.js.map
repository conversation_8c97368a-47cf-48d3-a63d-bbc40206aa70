{"version": 3, "file": "withAndroidNavigationBar.js", "names": ["_configPlugins", "data", "require", "NAVIGATION_BAR_COLOR", "withNavigationBar", "config", "immersiveMode", "getNavigationBarImmersiveMode", "WarningAggregator", "addWarningAndroid", "withNavigationBarColors", "withNavigationBarStyles", "exports", "withAndroidColors", "modResults", "setNavigationBarColors", "withAndroidStyles", "setNavigationBarStyles", "colors", "hexString", "getNavigationBarColor", "AndroidConfig", "Colors", "setColorItem", "Resources", "buildResourceItem", "name", "value", "styles", "Styles", "assignStylesValue", "add", "getNavigationBarStyle", "parent", "getAppThemeGroup", "androidNavigationBar", "visible", "backgroundColor", "barStyle"], "sources": ["../../../../src/plugins/unversioned/expo-navigation-bar/withAndroidNavigationBar.ts"], "sourcesContent": ["import {\n  AndroidConfig,\n  ConfigPlugin,\n  WarningAggregator,\n  withAndroidColors,\n  withAndroidStyles,\n} from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\n\nconst NAVIGATION_BAR_COLOR = 'navigationBarColor';\n\nexport const withNavigationBar: ConfigPlugin = (config) => {\n  const immersiveMode = getNavigationBarImmersiveMode(config);\n  if (immersiveMode) {\n    // Immersive mode needs to be set programmatically\n    WarningAggregator.addWarningAndroid(\n      'androidNavigationBar.visible',\n      'Property is deprecated in Android 11 (API 30) and will be removed from Expo SDK.',\n      'https://expo.fyi/android-navigation-bar-visible-deprecated'\n    );\n  }\n\n  config = withNavigationBarColors(config);\n  config = withNavigationBarStyles(config);\n  return config;\n};\n\nconst withNavigationBarColors: ConfigPlugin = (config) => {\n  return withAndroidColors(config, (config) => {\n    config.modResults = setNavigationBarColors(config, config.modResults);\n    return config;\n  });\n};\n\nconst withNavigationBarStyles: ConfigPlugin = (config) => {\n  return withAndroidStyles(config, (config) => {\n    config.modResults = setNavigationBarStyles(config, config.modResults);\n    return config;\n  });\n};\n\nexport function setNavigationBarColors(\n  config: Pick<ExpoConfig, 'androidNavigationBar'>,\n  colors: AndroidConfig.Resources.ResourceXML\n): AndroidConfig.Resources.ResourceXML {\n  const hexString = getNavigationBarColor(config);\n  if (hexString) {\n    colors = AndroidConfig.Colors.setColorItem(\n      AndroidConfig.Resources.buildResourceItem({\n        name: NAVIGATION_BAR_COLOR,\n        value: hexString,\n      }),\n      colors\n    );\n  }\n  return colors;\n}\n\nexport function setNavigationBarStyles(\n  config: Pick<ExpoConfig, 'androidNavigationBar'>,\n  styles: AndroidConfig.Resources.ResourceXML\n): AndroidConfig.Resources.ResourceXML {\n  styles = AndroidConfig.Styles.assignStylesValue(styles, {\n    add: getNavigationBarStyle(config) === 'dark-content',\n    parent: AndroidConfig.Styles.getAppThemeGroup(),\n    name: 'android:windowLightNavigationBar',\n    value: 'true',\n  });\n  styles = AndroidConfig.Styles.assignStylesValue(styles, {\n    add: !!getNavigationBarColor(config),\n    parent: AndroidConfig.Styles.getAppThemeGroup(),\n    name: `android:${NAVIGATION_BAR_COLOR}`,\n    value: `@color/${NAVIGATION_BAR_COLOR}`,\n  });\n\n  return styles;\n}\n\nexport function getNavigationBarImmersiveMode(config: Pick<ExpoConfig, 'androidNavigationBar'>) {\n  return config.androidNavigationBar?.visible || null;\n}\n\nexport function getNavigationBarColor(config: Pick<ExpoConfig, 'androidNavigationBar'>) {\n  return config.androidNavigationBar?.backgroundColor || null;\n}\n\nexport function getNavigationBarStyle(config: Pick<ExpoConfig, 'androidNavigationBar'>) {\n  return config.androidNavigationBar?.barStyle || 'light-content';\n}\n"], "mappings": ";;;;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AASA,MAAME,oBAAoB,GAAG,oBAAoB;AAE1C,MAAMC,iBAA+B,GAAIC,MAAM,IAAK;EACzD,MAAMC,aAAa,GAAGC,6BAA6B,CAACF,MAAM,CAAC;EAC3D,IAAIC,aAAa,EAAE;IACjB;IACAE,kCAAiB,CAACC,iBAAiB,CACjC,8BAA8B,EAC9B,kFAAkF,EAClF,4DACF,CAAC;EACH;EAEAJ,MAAM,GAAGK,uBAAuB,CAACL,MAAM,CAAC;EACxCA,MAAM,GAAGM,uBAAuB,CAACN,MAAM,CAAC;EACxC,OAAOA,MAAM;AACf,CAAC;AAACO,OAAA,CAAAR,iBAAA,GAAAA,iBAAA;AAEF,MAAMM,uBAAqC,GAAIL,MAAM,IAAK;EACxD,OAAO,IAAAQ,kCAAiB,EAACR,MAAM,EAAGA,MAAM,IAAK;IAC3CA,MAAM,CAACS,UAAU,GAAGC,sBAAsB,CAACV,MAAM,EAAEA,MAAM,CAACS,UAAU,CAAC;IACrE,OAAOT,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAMM,uBAAqC,GAAIN,MAAM,IAAK;EACxD,OAAO,IAAAW,kCAAiB,EAACX,MAAM,EAAGA,MAAM,IAAK;IAC3CA,MAAM,CAACS,UAAU,GAAGG,sBAAsB,CAACZ,MAAM,EAAEA,MAAM,CAACS,UAAU,CAAC;IACrE,OAAOT,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAEM,SAASU,sBAAsBA,CACpCV,MAAgD,EAChDa,MAA2C,EACN;EACrC,MAAMC,SAAS,GAAGC,qBAAqB,CAACf,MAAM,CAAC;EAC/C,IAAIc,SAAS,EAAE;IACbD,MAAM,GAAGG,8BAAa,CAACC,MAAM,CAACC,YAAY,CACxCF,8BAAa,CAACG,SAAS,CAACC,iBAAiB,CAAC;MACxCC,IAAI,EAAEvB,oBAAoB;MAC1BwB,KAAK,EAAER;IACT,CAAC,CAAC,EACFD,MACF,CAAC;EACH;EACA,OAAOA,MAAM;AACf;AAEO,SAASD,sBAAsBA,CACpCZ,MAAgD,EAChDuB,MAA2C,EACN;EACrCA,MAAM,GAAGP,8BAAa,CAACQ,MAAM,CAACC,iBAAiB,CAACF,MAAM,EAAE;IACtDG,GAAG,EAAEC,qBAAqB,CAAC3B,MAAM,CAAC,KAAK,cAAc;IACrD4B,MAAM,EAAEZ,8BAAa,CAACQ,MAAM,CAACK,gBAAgB,CAAC,CAAC;IAC/CR,IAAI,EAAE,kCAAkC;IACxCC,KAAK,EAAE;EACT,CAAC,CAAC;EACFC,MAAM,GAAGP,8BAAa,CAACQ,MAAM,CAACC,iBAAiB,CAACF,MAAM,EAAE;IACtDG,GAAG,EAAE,CAAC,CAACX,qBAAqB,CAACf,MAAM,CAAC;IACpC4B,MAAM,EAAEZ,8BAAa,CAACQ,MAAM,CAACK,gBAAgB,CAAC,CAAC;IAC/CR,IAAI,EAAE,WAAWvB,oBAAoB,EAAE;IACvCwB,KAAK,EAAE,UAAUxB,oBAAoB;EACvC,CAAC,CAAC;EAEF,OAAOyB,MAAM;AACf;AAEO,SAASrB,6BAA6BA,CAACF,MAAgD,EAAE;EAC9F,OAAOA,MAAM,CAAC8B,oBAAoB,EAAEC,OAAO,IAAI,IAAI;AACrD;AAEO,SAAShB,qBAAqBA,CAACf,MAAgD,EAAE;EACtF,OAAOA,MAAM,CAAC8B,oBAAoB,EAAEE,eAAe,IAAI,IAAI;AAC7D;AAEO,SAASL,qBAAqBA,CAAC3B,MAAgD,EAAE;EACtF,OAAOA,MAAM,CAAC8B,oBAAoB,EAAEG,QAAQ,IAAI,eAAe;AACjE", "ignoreList": []}