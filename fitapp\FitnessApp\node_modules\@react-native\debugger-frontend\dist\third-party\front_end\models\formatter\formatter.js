import*as t from"../../core/common/common.js";import*as e from"../../core/platform/platform.js";const r=Math.max(2,navigator.hardwareConcurrency-1);let n;class s{taskQueue;workerTasks;constructor(){this.taskQueue=[],this.workerTasks=new Map}static instance(){return n||(n=new s),n}createWorker(){const e=t.Worker.WorkerWrapper.fromURL(new URL("../../entrypoints/formatter_worker/formatter_worker-entrypoint.js",import.meta.url));return e.onmessage=this.onWorkerMessage.bind(this,e),e.onerror=this.onWorkerError.bind(this,e),e}processNextTask(){if(!this.taskQueue.length)return;let t=[...this.workerTasks.keys()].find((t=>!this.workerTasks.get(t)));if(!t&&this.workerTasks.size<r&&(t=this.createWorker()),!t)return;const e=this.taskQueue.shift();e&&(this.workerTasks.set(t,e),t.postMessage({method:e.method,params:e.params}))}onWorkerMessage(t,e){const r=this.workerTasks.get(t);r&&(r.isChunked&&e.data&&!e.data.isLastChunk?r.callback(e.data):(this.workerTasks.set(t,null),this.processNextTask(),r.callback(e.data?e.data:null)))}onWorkerError(t,e){console.error(e);const r=this.workerTasks.get(t);t.terminate(),this.workerTasks.delete(t);const n=this.createWorker();this.workerTasks.set(n,null),this.processNextTask(),r&&r.callback(null)}runChunkedTask(t,e,r){const n=new i(t,e,(function(t){if(!t)return void r(!0,null);const e="isLastChunk"in t&&Boolean(t.isLastChunk),n="chunk"in t&&t.chunk;r(e,n)}),!0);this.taskQueue.push(n),this.processNextTask()}runTask(t,e){return new Promise((r=>{const n=new i(t,e,r,!1);this.taskQueue.push(n),this.processNextTask()}))}format(t,e,r){const n={mimeType:t,content:e,indentString:r};return this.runTask("format",n)}javaScriptSubstitute(t,e){return this.runTask("javaScriptSubstitute",{content:t,mapping:e}).then((t=>t||""))}javaScriptScopeTree(t,e="script"){return this.runTask("javaScriptScopeTree",{content:t,sourceType:e}).then((t=>t||null))}evaluatableJavaScriptSubstring(t){return this.runTask("evaluatableJavaScriptSubstring",{content:t}).then((t=>t||""))}parseCSS(t,e){this.runChunkedTask("parseCSS",{content:t},(function(t,r){e(t,r||[])}))}}class i{method;params;callback;isChunked;constructor(t,e,r,n){this.method=t,this.params=e,this.callback=r,this.isChunked=n}}function o(){return s.instance()}var a=Object.freeze({__proto__:null,FormatterWorkerPool:s,formatterWorkerPool:o});function c(t,e,r){return(e?t[e-1]+1:0)+r}function u(t,r){const n=e.ArrayUtilities.upperBound(t,r-1,e.ArrayUtilities.DEFAULT_COMPARATOR);let s;return s=n?r-t[n-1]-1:r,[n,s]}async function h(r,n,s=t.Settings.Settings.instance().moduleSetting("text-editor-indent").get()){const i=n.replace(/\r\n?|[\n\u2028\u2029]/g,"\n").replace(/^\uFEFF/,""),a=o(),c=await a.format(r,i,s),u=e.StringUtilities.findLineEndingIndexes(i),h=e.StringUtilities.findLineEndingIndexes(c.content),k=new l(u,h,c.mapping);return{formattedContent:c.content,formattedMapping:k}}class k{originalToFormatted(t,e=0){return[t,e]}formattedToOriginal(t,e=0){return[t,e]}}class l{originalLineEndings;formattedLineEndings;mapping;constructor(t,e,r){this.originalLineEndings=t,this.formattedLineEndings=e,this.mapping=r}originalToFormatted(t,e){const r=c(this.originalLineEndings,t,e||0),n=this.convertPosition(this.mapping.original,this.mapping.formatted,r);return u(this.formattedLineEndings,n)}formattedToOriginal(t,e){const r=c(this.formattedLineEndings,t,e||0),n=this.convertPosition(this.mapping.formatted,this.mapping.original,r);return u(this.originalLineEndings,n)}convertPosition(t,r,n){const s=e.ArrayUtilities.upperBound(t,n,e.ArrayUtilities.DEFAULT_COMPARATOR)-1;let i=r[s]+n-t[s];return s<r.length-1&&i>r[s+1]&&(i=r[s+1]),i}}var p=Object.freeze({__proto__:null,format:async function(e,r,n,s=t.Settings.Settings.instance().moduleSetting("text-editor-indent").get()){return e.isDocumentOrScriptOrStyleSheet()?h(r,n,s):{formattedContent:n,formattedMapping:new k}},formatScriptContent:h});export{a as FormatterWorkerPool,p as ScriptFormatter};
