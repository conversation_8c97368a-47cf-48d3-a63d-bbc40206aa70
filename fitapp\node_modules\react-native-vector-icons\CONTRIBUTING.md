# Contributing

Thank for taking the time to check out the repo and be willing to contribute!

If you have found an issue or would like to request a new feature, simply create a new issue. Be sure to fill out as much information as possible.

If this is your first open source contribution, please take a look at [this](https://egghead.io/courses/how-to-contribute-to-an-open-source-project-on-github) guide.

## Reporting Bugs & Feature Requests

If you would like to submit a feature request or report a bug, we encourage you to first look through the [issues](https://github.com/oblador/react-native-vector-icons/issues) and [pull requests](https://github.com/oblador/react-native-vector-icons/pulls) before filing a new issue.

## Submitting a Pull Request

If you wish to submit a pull request for a new feature or issue, you should start by forking this repository first. This should get you setup on your local machine:

### Setup

- Install [Node.js](https://nodejs.org/) if you have not already. (_We suggest you to use node v16.x.x_)
- Fork the repo
- `git clone https://github.com/*yourusername*/react-native-vector-icons.git && cd react-native-vector-icons`
- `yarn install` OR `npm install`
- `npm test`
- Optionally install [Docker](https://www.docker.com/products/docker-desktop) and run `npm run build` OR `yarn build` if you've updated any icon sets.

One you have done this, create a new branch with a name that loosely describes the issue on which you will be working. Once you think you have the addressed the issue in question, submit a pull request to the `master` branch.
