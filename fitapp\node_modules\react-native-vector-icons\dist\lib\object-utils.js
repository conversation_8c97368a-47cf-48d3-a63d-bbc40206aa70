var pick=function pick(obj){for(var _len=arguments.length,keys=new Array(_len>1?_len-1:0),_key=1;_key<_len;_key++){keys[_key-1]=arguments[_key];}return keys.flat().filter(function(key){return Object.prototype.hasOwnProperty.call(obj,key);}).reduce(function(acc,key){acc[key]=obj[key];return acc;},{});};var omit=function omit(obj){for(var _len2=arguments.length,keysToOmit=new Array(_len2>1?_len2-1:0),_key2=1;_key2<_len2;_key2++){keysToOmit[_key2-1]=arguments[_key2];}var keysToOmitSet=new Set(keysToOmit.flat());return Object.getOwnPropertyNames(obj).filter(function(key){return!keysToOmitSet.has(key);}).reduce(function(acc,key){acc[key]=obj[key];return acc;},{});};module.exports={pick:pick,omit:omit};