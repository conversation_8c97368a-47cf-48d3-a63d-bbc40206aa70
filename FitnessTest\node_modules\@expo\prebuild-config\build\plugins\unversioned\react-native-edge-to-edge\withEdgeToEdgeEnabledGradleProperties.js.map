{"version": 3, "file": "withEdgeToEdgeEnabledGradleProperties.js", "names": ["_configPlugins", "data", "require", "EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY", "EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT", "withEdgeToEdgeEnabledGradleProperties", "config", "props", "withGradleProperties", "configureEdgeToEdgeEnabledGradleProperties", "edgeToEdgeEnabled", "propertyIndex", "modResults", "findIndex", "item", "type", "key", "splice", "commentIndex", "value", "push"], "sources": ["../../../../src/plugins/unversioned/react-native-edge-to-edge/withEdgeToEdgeEnabledGradleProperties.ts"], "sourcesContent": ["import { withGradleProperties } from '@expo/config-plugins';\nimport type { ExpoConfig } from '@expo/config-types';\n\nimport { GradlePropertiesConfig } from './withEdgeToEdge';\n\nconst EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY = 'expo.edgeToEdgeEnabled';\nconst EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT =\n  'Whether the app is configured to use edge-to-edge via the app config or `react-native-edge-to-edge` plugin';\n\nexport function withEdgeToEdgeEnabledGradleProperties(\n  config: ExpoConfig,\n  props: {\n    edgeToEdgeEnabled: boolean;\n  }\n) {\n  return withGradleProperties(config, (config) => {\n    return configureEdgeToEdgeEnabledGradleProperties(config, props.edgeToEdgeEnabled);\n  });\n}\n\nexport function configureEdgeToEdgeEnabledGradleProperties(\n  config: GradlePropertiesConfig,\n  edgeToEdgeEnabled: boolean\n): GradlePropertiesConfig {\n  const propertyIndex = config.modResults.findIndex(\n    (item) => item.type === 'property' && item.key === EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY\n  );\n  if (propertyIndex !== -1) {\n    config.modResults.splice(propertyIndex, 1);\n  }\n  const commentIndex = config.modResults.findIndex(\n    (item) => item.type === 'comment' && item.value === EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT\n  );\n  if (commentIndex !== -1) {\n    config.modResults.splice(commentIndex, 1);\n  }\n\n  config.modResults.push({\n    type: 'comment',\n    value: EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT,\n  });\n  config.modResults.push({\n    type: 'property',\n    key: EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY,\n    value: edgeToEdgeEnabled ? 'true' : 'false',\n  });\n\n  return config;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKA,MAAME,wCAAwC,GAAG,wBAAwB;AACzE,MAAMC,4CAA4C,GAChD,4GAA4G;AAEvG,SAASC,qCAAqCA,CACnDC,MAAkB,EAClBC,KAEC,EACD;EACA,OAAO,IAAAC,qCAAoB,EAACF,MAAM,EAAGA,MAAM,IAAK;IAC9C,OAAOG,0CAA0C,CAACH,MAAM,EAAEC,KAAK,CAACG,iBAAiB,CAAC;EACpF,CAAC,CAAC;AACJ;AAEO,SAASD,0CAA0CA,CACxDH,MAA8B,EAC9BI,iBAA0B,EACF;EACxB,MAAMC,aAAa,GAAGL,MAAM,CAACM,UAAU,CAACC,SAAS,CAC9CC,IAAI,IAAKA,IAAI,CAACC,IAAI,KAAK,UAAU,IAAID,IAAI,CAACE,GAAG,KAAKb,wCACrD,CAAC;EACD,IAAIQ,aAAa,KAAK,CAAC,CAAC,EAAE;IACxBL,MAAM,CAACM,UAAU,CAACK,MAAM,CAACN,aAAa,EAAE,CAAC,CAAC;EAC5C;EACA,MAAMO,YAAY,GAAGZ,MAAM,CAACM,UAAU,CAACC,SAAS,CAC7CC,IAAI,IAAKA,IAAI,CAACC,IAAI,KAAK,SAAS,IAAID,IAAI,CAACK,KAAK,KAAKf,4CACtD,CAAC;EACD,IAAIc,YAAY,KAAK,CAAC,CAAC,EAAE;IACvBZ,MAAM,CAACM,UAAU,CAACK,MAAM,CAACC,YAAY,EAAE,CAAC,CAAC;EAC3C;EAEAZ,MAAM,CAACM,UAAU,CAACQ,IAAI,CAAC;IACrBL,IAAI,EAAE,SAAS;IACfI,KAAK,EAAEf;EACT,CAAC,CAAC;EACFE,MAAM,CAACM,UAAU,CAACQ,IAAI,CAAC;IACrBL,IAAI,EAAE,UAAU;IAChBC,GAAG,EAAEb,wCAAwC;IAC7CgB,KAAK,EAAET,iBAAiB,GAAG,MAAM,GAAG;EACtC,CAAC,CAAC;EAEF,OAAOJ,MAAM;AACf", "ignoreList": []}