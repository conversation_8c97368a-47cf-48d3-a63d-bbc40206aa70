import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Provider as PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

// Screens
import HomeScreen from './src/screens/HomeScreen';
import BMIScreen from './src/screens/BMIScreen';
import NutritionScreen from './src/screens/NutritionScreen';
import WorkoutScreen from './src/screens/WorkoutScreen';
import ProfileScreen from './src/screens/ProfileScreen';

// Theme
import { theme } from './src/theme/theme';

const Tab = createBottomTabNavigator();

export default function App() {
  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <NavigationContainer>
          <StatusBar style="light" backgroundColor="#1976d2" />
          <Tab.Navigator
            screenOptions={({ route }) => ({
              tabBarIcon: ({ focused, color, size }) => {
                let iconName: keyof typeof Ionicons.glyphMap;

                if (route.name === 'Ana Sayfa') {
                  iconName = focused ? 'home' : 'home-outline';
                } else if (route.name === 'BMI') {
                  iconName = focused ? 'calculator' : 'calculator-outline';
                } else if (route.name === 'Beslenme') {
                  iconName = focused ? 'restaurant' : 'restaurant-outline';
                } else if (route.name === 'Antrenman') {
                  iconName = focused ? 'fitness' : 'fitness-outline';
                } else if (route.name === 'Profil') {
                  iconName = focused ? 'person' : 'person-outline';
                } else {
                  iconName = 'help-outline';
                }

                return <Ionicons name={iconName} size={size} color={color} />;
              },
              tabBarActiveTintColor: '#1976d2',
              tabBarInactiveTintColor: 'gray',
              headerStyle: {
                backgroundColor: '#1976d2',
              },
              headerTintColor: '#fff',
              headerTitleStyle: {
                fontWeight: 'bold',
              },
            })}
          >
            <Tab.Screen name="Ana Sayfa" component={HomeScreen} />
            <Tab.Screen name="BMI" component={BMIScreen} />
            <Tab.Screen name="Beslenme" component={NutritionScreen} />
            <Tab.Screen name="Antrenman" component={WorkoutScreen} />
            <Tab.Screen name="Profil" component={ProfileScreen} />
          </Tab.Navigator>
        </NavigationContainer>
      </PaperProvider>
    </SafeAreaProvider>
  );
}
