import React from 'react';
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Alert,
} from 'react-native';

function App(): React.JSX.Element {
  const handleBMICalculate = () => {
    Alert.alert('BMI Hesaplama', 'BMI hesaplama özelliği yakında!');
  };

  const handleNutrition = () => {
    Alert.alert('Beslenme', 'Beslenme planı özelliği yakında!');
  };

  const handleWorkout = () => {
    Alert.alert('Antrenman', 'Antrenman programı özelliği yakında!');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#1976d2" barStyle="light-content" />
      <ScrollView contentInsetAdjustmentBehavior="automatic">
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>💪 Fitness App</Text>
          <Text style={styles.headerSubtitle}><PERSON>ğlık<PERSON><PERSON> yaşamın başlangıcı</Text>
        </View>

        {/* Main Content */}
        <View style={styles.content}>
          <Text style={styles.welcomeText}>Hoş Geldiniz!</Text>
          <Text style={styles.descriptionText}>
            Kişiselleştirilmiş fitness ve beslenme planlarınız için hazırız.
          </Text>

          {/* Feature Cards */}
          <View style={styles.cardContainer}>
            <TouchableOpacity style={styles.card} onPress={handleBMICalculate}>
              <Text style={styles.cardIcon}>📊</Text>
              <Text style={styles.cardTitle}>BMI Hesaplama</Text>
              <Text style={styles.cardDescription}>
                Boy ve kilonuza göre BMI hesaplayın
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.card} onPress={handleNutrition}>
              <Text style={styles.cardIcon}>🥗</Text>
              <Text style={styles.cardTitle}>Beslenme Planı</Text>
              <Text style={styles.cardDescription}>
                Kişiselleştirilmiş beslenme önerileri
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.card} onPress={handleWorkout}>
              <Text style={styles.cardIcon}>🏋️</Text>
              <Text style={styles.cardTitle}>Antrenman</Text>
              <Text style={styles.cardDescription}>
                Size özel egzersiz programları
              </Text>
            </TouchableOpacity>
          </View>

          {/* Stats */}
          <View style={styles.statsContainer}>
            <Text style={styles.statsTitle}>Günlük Hedefler</Text>
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>2.5L</Text>
                <Text style={styles.statLabel}>Su</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>8000</Text>
                <Text style={styles.statLabel}>Adım</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>30dk</Text>
                <Text style={styles.statLabel}>Egzersiz</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#1976d2',
    padding: 30,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#e3f2fd',
  },
  content: {
    padding: 20,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  descriptionText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  cardContainer: {
    marginBottom: 30,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  cardIcon: {
    fontSize: 32,
    textAlign: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 5,
  },
  cardDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  statsContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
  },
});

export default App;
