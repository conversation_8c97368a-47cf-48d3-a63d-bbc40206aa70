import type { BMIResult } from '../schemas/fitness-data.js';
import type { UserProfile } from '../schemas/user-profile.js';

// BMI hesaplama fonksiyonu
export function calculateBMI(height: number, weight: number): BMIResult {
  // Boy cm'den m'ye çevir
  const heightInMeters = height / 100;
  const bmi = weight / (heightInMeters * heightInMeters);
  
  let category: BMIResult['category'];
  let categoryDescription: string;
  let healthRisk: string;
  let recommendations: string[];

  // BMI kategorilerini belirle
  if (bmi < 18.5) {
    category = 'zayıf';
    categoryDescription = 'Zayıf (Düşük Kilolu)';
    healthRisk = 'Beslenme eksikliği, bağışıklık sistemi zayıflığı riski';
    recommendations = [
      'Sağlıklı kilo almak için kalori alımını artırın',
      'Protein açısından zengin besinler tüketin',
      '<PERSON><PERSON>ç antrenmanı yaparak kas kütlesi kazanın',
      'Bir diyetisyene danışın'
    ];
  } else if (bmi >= 18.5 && bmi < 25) {
    category = 'normal';
    categoryDescription = 'Normal Kilo';
    healthRisk = 'En düşük hastalık riski';
    recommendations = [
      'Mevcut kiloyu korumaya odaklanın',
      'Dengeli beslenme alışkanlığını sürdürün',
      'Düzenli egzersiz yapın',
      'Sağlıklı yaşam tarzını devam ettirin'
    ];
  } else if (bmi >= 25 && bmi < 30) {
    category = 'fazla_kilolu';
    categoryDescription = 'Fazla Kilolu';
    healthRisk = 'Orta düzeyde sağlık riski';
    recommendations = [
      'Haftada 0.5-1 kg kilo vermeyi hedefleyin',
      'Kalori alımını azaltın ve fiziksel aktiviteyi artırın',
      'Porsiyon kontrolü yapın',
      'Su tüketimini artırın'
    ];
  } else if (bmi >= 30 && bmi < 35) {
    category = 'obez_1';
    categoryDescription = 'Obezite (1. Derece)';
    healthRisk = 'Yüksek sağlık riski - diyabet, kalp hastalığı riski';
    recommendations = [
      'Doktor kontrolünde kilo verme programı başlatın',
      'Günlük kalori alımını önemli ölçüde azaltın',
      'Düşük etkili egzersizlerle başlayın',
      'Beslenme uzmanından destek alın'
    ];
  } else if (bmi >= 35 && bmi < 40) {
    category = 'obez_2';
    categoryDescription = 'Obezite (2. Derece)';
    healthRisk = 'Çok yüksek sağlık riski';
    recommendations = [
      'Acil olarak doktora başvurun',
      'Profesyonel kilo verme programına katılın',
      'Medikal destek alın',
      'Yaşam tarzı değişikliği için uzman desteği alın'
    ];
  } else {
    category = 'obez_3';
    categoryDescription = 'Morbid Obezite (3. Derece)';
    healthRisk = 'Aşırı yüksek sağlık riski - yaşamı tehdit edici';
    recommendations = [
      'Derhal doktora başvurun',
      'Bariatrik cerrahi seçeneklerini değerlendirin',
      'Yoğun medikal takip gerekli',
      'Multidisipliner tedavi ekibi ile çalışın'
    ];
  }

  // İdeal kilo aralığını hesapla (BMI 18.5-24.9 için)
  const heightInMetersSquared = heightInMeters * heightInMeters;
  const idealWeightRange = {
    min: Math.round(18.5 * heightInMetersSquared * 10) / 10,
    max: Math.round(24.9 * heightInMetersSquared * 10) / 10
  };

  return {
    bmi: Math.round(bmi * 10) / 10,
    category,
    categoryDescription,
    healthRisk,
    recommendations,
    idealWeightRange
  };
}

// Günlük kalori ihtiyacını hesapla (Harris-Benedict formülü)
export function calculateDailyCalorieNeeds(userProfile: UserProfile): number {
  const { age, gender, height, weight, activityLevel } = userProfile;
  
  // Bazal Metabolizma Hızı (BMR) hesapla
  let bmr: number;
  if (gender === 'erkek') {
    bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
  } else {
    bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
  }

  // Aktivite seviyesi çarpanları
  const activityMultipliers = {
    'sedanter': 1.2,
    'hafif_aktif': 1.375,
    'orta_aktif': 1.55,
    'çok_aktif': 1.725,
    'aşırı_aktif': 1.9
  };

  const dailyCalories = bmr * activityMultipliers[activityLevel];
  return Math.round(dailyCalories);
}

// Makrobesin dağılımını hesapla
export function calculateMacronutrients(dailyCalories: number, goal: string = 'genel_sağlık') {
  let proteinPercentage: number;
  let carbPercentage: number;
  let fatPercentage: number;

  switch (goal) {
    case 'kilo_verme':
      proteinPercentage = 30;
      carbPercentage = 40;
      fatPercentage = 30;
      break;
    case 'kas_kazanma':
      proteinPercentage = 25;
      carbPercentage = 45;
      fatPercentage = 30;
      break;
    case 'kilo_alma':
      proteinPercentage = 20;
      carbPercentage = 50;
      fatPercentage = 30;
      break;
    default:
      proteinPercentage = 20;
      carbPercentage = 50;
      fatPercentage = 30;
  }

  return {
    protein: {
      grams: Math.round((dailyCalories * proteinPercentage / 100) / 4),
      percentage: proteinPercentage
    },
    carbohydrates: {
      grams: Math.round((dailyCalories * carbPercentage / 100) / 4),
      percentage: carbPercentage
    },
    fats: {
      grams: Math.round((dailyCalories * fatPercentage / 100) / 9),
      percentage: fatPercentage
    }
  };
}
