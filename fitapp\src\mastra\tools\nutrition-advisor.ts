import { createTool } from '@mastra/core';
import { NutritionRequestSchema } from '../schemas/user-profile.js';
import { calculateDailyCalorieNeeds, calculateMacronutrients } from '../utils/fitness-calculations.js';
import { nutritionDatabase } from '../utils/nutrition-data.js';
import type { NutritionPlan } from '../schemas/fitness-data.js';

export const nutritionAdvisorTool = createTool({
  id: 'nutrition-advisor',
  description: 'Kullanıcının profili ve BMI kategorisine göre kişiselleştirilmiş beslenme planı ve önerileri sunar',
  inputSchema: NutritionRequestSchema,
  execute: async ({ context }) => {
    const { userProfile, bmiCategory, dailyCalorieNeeds } = context;
    
    try {
      // Ma<PERSON><PERSON><PERSON> dağılımını hesapla
      const primaryGoal = userProfile.fitnessGoals?.[0] || 'genel_sağlık';
      const macronutrients = calculateMacronutrients(dailyCalorieNeeds, primaryGoal);
      
      // BMI kategorisine göre beslenme önerilerini al
      const nutritionData = nutritionDatabase.nutritionByBMI[bmiCategory];
      
      // Hedefe göre öğün önerilerini al
      const goalKey = primaryGoal === 'kilo_verme' ? 'kilo_verme' : 
                     primaryGoal === 'kilo_alma' ? 'kilo_alma' : 'normal';
      
      const mealSuggestions = nutritionDatabase.mealSuggestions;
      
      // Su ihtiyacını hesapla
      const waterNeeds = Math.round(userProfile.weight * 35); // ml/kg
      
      const nutritionPlan: NutritionPlan = {
        dailyCalorieNeeds,
        macronutrients,
        mealPlan: {
          breakfast: mealSuggestions.breakfast[goalKey] || mealSuggestions.breakfast.normal,
          lunch: mealSuggestions.lunch[goalKey] || mealSuggestions.lunch.normal,
          dinner: mealSuggestions.dinner[goalKey] || mealSuggestions.dinner.normal,
          snacks: mealSuggestions.snacks[goalKey] || mealSuggestions.snacks.normal
        },
        nutritionTips: [
          ...nutritionData.tips,
          `Günlük ${macronutrients.protein.grams}g protein alın`,
          `Karbonhidrat alımınızı ${macronutrients.carbohydrates.grams}g ile sınırlayın`,
          `Sağlıklı yağlardan günde ${macronutrients.fats.grams}g tüketin`,
          'Öğünlerinizi düzenli saatlerde alın',
          'Yemek yerken yavaş çiğneyin'
        ],
        foodsToAvoid: nutritionData.avoid,
        hydrationGoal: `Günde en az ${waterNeeds}ml (yaklaşık ${Math.ceil(waterNeeds/250)} bardak) su için`
      };
      
      // Özel durumlar için ek öneriler
      const additionalTips = [];
      
      if (userProfile.allergies && userProfile.allergies.length > 0) {
        additionalTips.push(`Alerjiniz olan gıdalardan kaçının: ${userProfile.allergies.join(', ')}`);
      }
      
      if (userProfile.healthConditions && userProfile.healthConditions.length > 0) {
        additionalTips.push('Sağlık durumunuz nedeniyle beslenme uzmanına danışın');
      }
      
      if (userProfile.age > 65) {
        additionalTips.push('Yaşınız nedeniyle kalsiyum ve D vitamini alımına özen gösterin');
      }
      
      if (userProfile.gender === 'kadın' && userProfile.age >= 18 && userProfile.age <= 50) {
        additionalTips.push('Demir eksikliğine karşı demir açısından zengin besinler tüketin');
      }
      
      nutritionPlan.nutritionTips.push(...additionalTips);
      
      return {
        success: true,
        data: nutritionPlan,
        message: `${userProfile.name} için kişiselleştirilmiş beslenme planı hazırlandı. Günlük kalori ihtiyacınız: ${dailyCalorieNeeds} kalori`
      };
      
    } catch (error) {
      return {
        success: false,
        error: 'Beslenme planı oluşturulurken bir hata oluştu',
        details: error instanceof Error ? error.message : 'Bilinmeyen hata'
      };
    }
  }
});
