{"version": 3, "file": "withAndroidIcons.js", "names": ["_configPlugins", "data", "require", "_imageUtils", "_fs", "_interopRequireDefault", "_path", "_withAndroidManifestIcons", "e", "__esModule", "default", "Colors", "AndroidConfig", "dpi<PERSON><PERSON><PERSON>", "exports", "mdpi", "folderName", "scale", "hdpi", "xhdpi", "xxhdpi", "xxxhdpi", "LEGACY_BASELINE_PIXEL_SIZE", "ADAPTIVE_BASELINE_PIXEL_SIZE", "ANDROID_RES_PATH", "MIPMAP_ANYDPI_V26", "ICON_BACKGROUND", "IC_LAUNCHER_WEBP", "IC_LAUNCHER_ROUND_WEBP", "IC_LAUNCHER_BACKGROUND_WEBP", "IC_LAUNCHER_FOREGROUND_WEBP", "IC_LAUNCHER_MONOCHROME_WEBP", "IC_LAUNCHER_XML", "IC_LAUNCHER_ROUND_XML", "withAndroidIcons", "config", "foregroundImage", "backgroundColor", "backgroundImage", "monochromeImage", "getAdaptiveIcon", "icon", "getIcon", "withAndroidManifestIcons", "withAndroidAdaptiveIconColors", "withDangerousMod", "setIconAsync", "modRequest", "projectRoot", "isAdaptive", "android", "adaptiveIcon", "setRoundIconManifest", "manifest", "application", "Manifest", "getMainApplicationOrThrow", "$", "withAndroidColors", "modResults", "setBackgroundColor", "configureLegacyIconAsync", "generateRoundIconAsync", "deleteIconNamedAsync", "configureAdaptiveIconAsync", "generateMultiLayerImageAsync", "outputImageFileName", "imageCacheFolder", "backgroundImageCacheFolder", "borderRadiusRatio", "generateMonochromeImageAsync", "backgroundImageFileName", "icLauncherXmlString", "createAdaptiveIconXmlString", "createAdaptiveIconXmlFiles", "colors", "assignColorValue", "value", "name", "background", "iconElements", "push", "join", "add", "anyDpiV26Directory", "path", "resolve", "fs", "promises", "mkdir", "recursive", "launcherPath", "launcherRoundPath", "Promise", "all", "writeFile", "map", "rm", "force", "iterateDpiValues", "dpiFolder", "icon<PERSON><PERSON>er", "generateIconAsync", "cacheType", "src", "background<PERSON>ayer", "compositeImagesAsync", "foreground", "monochromeIcon", "callback", "Object", "values", "iconSizePx", "generateImageAsync", "width", "height", "resizeMode", "borderRadius", "undefined", "source"], "sources": ["../../../src/plugins/icons/withAndroidIcons.ts"], "sourcesContent": ["import {\n  AndroidConfig,\n  ConfigPlugin,\n  withAndroidColors,\n  withDangerousMod,\n} from '@expo/config-plugins';\nimport { ResourceXML } from '@expo/config-plugins/build/android/Resources';\nimport { ExpoConfig } from '@expo/config-types';\nimport { compositeImagesAsync, generateImageAsync } from '@expo/image-utils';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { withAndroidManifestIcons } from './withAndroidManifestIcons';\n\nconst { Colors } = AndroidConfig;\n\ntype DPIString = 'mdpi' | 'hdpi' | 'xhdpi' | 'xxhdpi' | 'xxxhdpi';\ntype dpiMap = Record<DPIString, { folderName: string; scale: number }>;\n\nexport const dpiValues: dpiMap = {\n  mdpi: { folderName: 'mipmap-mdpi', scale: 1 },\n  hdpi: { folderName: 'mipmap-hdpi', scale: 1.5 },\n  xhdpi: { folderName: 'mipmap-xhdpi', scale: 2 },\n  xxhdpi: { folderName: 'mipmap-xxhdpi', scale: 3 },\n  xxxhdpi: { folderName: 'mipmap-xxxhdpi', scale: 4 },\n};\n\nconst LEGACY_BASELINE_PIXEL_SIZE = 48;\nconst ADAPTIVE_BASELINE_PIXEL_SIZE = 108;\n\nexport const ANDROID_RES_PATH = 'android/app/src/main/res/';\nconst MIPMAP_ANYDPI_V26 = 'mipmap-anydpi-v26';\nconst ICON_BACKGROUND = 'iconBackground';\nconst IC_LAUNCHER_WEBP = 'ic_launcher.webp';\nconst IC_LAUNCHER_ROUND_WEBP = 'ic_launcher_round.webp';\nconst IC_LAUNCHER_BACKGROUND_WEBP = 'ic_launcher_background.webp';\nconst IC_LAUNCHER_FOREGROUND_WEBP = 'ic_launcher_foreground.webp';\nconst IC_LAUNCHER_MONOCHROME_WEBP = 'ic_launcher_monochrome.webp';\nconst IC_LAUNCHER_XML = 'ic_launcher.xml';\nconst IC_LAUNCHER_ROUND_XML = 'ic_launcher_round.xml';\n\nexport const withAndroidIcons: ConfigPlugin = (config) => {\n  const { foregroundImage, backgroundColor, backgroundImage, monochromeImage } =\n    getAdaptiveIcon(config);\n  const icon = foregroundImage ?? getIcon(config);\n\n  if (!icon) {\n    return config;\n  }\n\n  config = withAndroidManifestIcons(config);\n  // Apply colors.xml changes\n  config = withAndroidAdaptiveIconColors(config, backgroundColor);\n  return withDangerousMod(config, [\n    'android',\n    async (config) => {\n      await setIconAsync(config.modRequest.projectRoot, {\n        icon,\n        backgroundColor,\n        backgroundImage,\n        monochromeImage,\n        isAdaptive: !!config.android?.adaptiveIcon,\n      });\n      return config;\n    },\n  ]);\n};\n\nexport function setRoundIconManifest(\n  config: Pick<ExpoConfig, 'android'>,\n  manifest: AndroidConfig.Manifest.AndroidManifest\n): AndroidConfig.Manifest.AndroidManifest {\n  const isAdaptive = !!config.android?.adaptiveIcon;\n  const application = AndroidConfig.Manifest.getMainApplicationOrThrow(manifest);\n\n  if (isAdaptive) {\n    application.$['android:roundIcon'] = '@mipmap/ic_launcher_round';\n  } else {\n    delete application.$['android:roundIcon'];\n  }\n  return manifest;\n}\n\nconst withAndroidAdaptiveIconColors: ConfigPlugin<string | null> = (config, backgroundColor) => {\n  return withAndroidColors(config, (config) => {\n    config.modResults = setBackgroundColor(backgroundColor ?? '#ffffff', config.modResults);\n    return config;\n  });\n};\n\nexport function getIcon(config: ExpoConfig) {\n  return config.android?.icon || config.icon || null;\n}\n\nexport function getAdaptiveIcon(config: ExpoConfig) {\n  return {\n    foregroundImage: config.android?.adaptiveIcon?.foregroundImage ?? null,\n    backgroundColor: config.android?.adaptiveIcon?.backgroundColor ?? null,\n    backgroundImage: config.android?.adaptiveIcon?.backgroundImage ?? null,\n    monochromeImage: config.android?.adaptiveIcon?.monochromeImage ?? null,\n  };\n}\n\n/**\n * Resizes the user-provided icon to create a set of legacy icon files in\n * their respective \"mipmap\" directories for <= Android 7, and creates a set of adaptive\n * icon files for > Android 7 from the adaptive icon files (if provided).\n */\nexport async function setIconAsync(\n  projectRoot: string,\n  {\n    icon,\n    backgroundColor,\n    backgroundImage,\n    monochromeImage,\n    isAdaptive,\n  }: {\n    icon: string | null;\n    backgroundColor: string | null;\n    backgroundImage: string | null;\n    monochromeImage: string | null;\n    isAdaptive: boolean;\n  }\n) {\n  if (!icon) {\n    return null;\n  }\n\n  await configureLegacyIconAsync(projectRoot, icon, backgroundImage, backgroundColor);\n  if (isAdaptive) {\n    await generateRoundIconAsync(projectRoot, icon, backgroundImage, backgroundColor);\n  } else {\n    await deleteIconNamedAsync(projectRoot, IC_LAUNCHER_ROUND_WEBP);\n  }\n  await configureAdaptiveIconAsync(projectRoot, icon, backgroundImage, monochromeImage, isAdaptive);\n\n  return true;\n}\n\n/**\n * Configures legacy icon files to be used on Android 7 and earlier. If adaptive icon configuration\n * was provided, we create a pseudo-adaptive icon by layering the provided files (or background\n * color if no backgroundImage is provided. If no backgroundImage and no backgroundColor are provided,\n * the background is set to transparent.)\n */\nasync function configureLegacyIconAsync(\n  projectRoot: string,\n  icon: string,\n  backgroundImage: string | null,\n  backgroundColor: string | null\n) {\n  return generateMultiLayerImageAsync(projectRoot, {\n    icon,\n    backgroundImage,\n    backgroundColor,\n    outputImageFileName: IC_LAUNCHER_WEBP,\n    imageCacheFolder: 'android-standard-square',\n    backgroundImageCacheFolder: 'android-standard-square-background',\n  });\n}\n\nasync function generateRoundIconAsync(\n  projectRoot: string,\n  icon: string,\n  backgroundImage: string | null,\n  backgroundColor: string | null\n) {\n  return generateMultiLayerImageAsync(projectRoot, {\n    icon,\n    borderRadiusRatio: 0.5,\n    outputImageFileName: IC_LAUNCHER_ROUND_WEBP,\n    backgroundImage,\n    backgroundColor,\n    imageCacheFolder: 'android-standard-circle',\n    backgroundImageCacheFolder: 'android-standard-round-background',\n    isAdaptive: false,\n  });\n}\n\n/**\n * Configures adaptive icon files to be used on Android 8 and up. A foreground image must be provided,\n * and will have a transparent background unless:\n * - A backgroundImage is provided, or\n * - A backgroundColor was specified\n */\nexport async function configureAdaptiveIconAsync(\n  projectRoot: string,\n  foregroundImage: string,\n  backgroundImage: string | null,\n  monochromeImage: string | null,\n  isAdaptive: boolean\n) {\n  if (monochromeImage) {\n    await generateMonochromeImageAsync(projectRoot, {\n      icon: monochromeImage,\n      imageCacheFolder: 'android-adaptive-monochrome',\n      outputImageFileName: IC_LAUNCHER_MONOCHROME_WEBP,\n    });\n  }\n  await generateMultiLayerImageAsync(projectRoot, {\n    backgroundColor: 'transparent',\n    backgroundImage,\n    backgroundImageCacheFolder: 'android-adaptive-background',\n    outputImageFileName: IC_LAUNCHER_FOREGROUND_WEBP,\n    icon: foregroundImage,\n    imageCacheFolder: 'android-adaptive-foreground',\n    backgroundImageFileName: IC_LAUNCHER_BACKGROUND_WEBP,\n    isAdaptive: true,\n  });\n\n  // create ic_launcher.xml and ic_launcher_round.xml\n  const icLauncherXmlString = createAdaptiveIconXmlString(backgroundImage, monochromeImage);\n  await createAdaptiveIconXmlFiles(\n    projectRoot,\n    icLauncherXmlString,\n    // If the user only defined icon and not android.adaptiveIcon, then skip enabling the layering system\n    // this will scale the image down and present it uncropped.\n    isAdaptive\n  );\n}\n\nfunction setBackgroundColor(backgroundColor: string | null, colors: ResourceXML) {\n  return Colors.assignColorValue(colors, {\n    value: backgroundColor,\n    name: ICON_BACKGROUND,\n  });\n}\n\nexport const createAdaptiveIconXmlString = (\n  backgroundImage: string | null,\n  monochromeImage: string | null\n) => {\n  const background = backgroundImage ? `@mipmap/ic_launcher_background` : `@color/iconBackground`;\n\n  const iconElements: string[] = [\n    `<background android:drawable=\"${background}\"/>`,\n    '<foreground android:drawable=\"@mipmap/ic_launcher_foreground\"/>',\n  ];\n\n  if (monochromeImage) {\n    iconElements.push('<monochrome android:drawable=\"@mipmap/ic_launcher_monochrome\"/>');\n  }\n\n  return `<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<adaptive-icon xmlns:android=\"http://schemas.android.com/apk/res/android\">\n    ${iconElements.join('\\n    ')}\n</adaptive-icon>`;\n};\n\nasync function createAdaptiveIconXmlFiles(\n  projectRoot: string,\n  icLauncherXmlString: string,\n  add: boolean\n) {\n  const anyDpiV26Directory = path.resolve(projectRoot, ANDROID_RES_PATH, MIPMAP_ANYDPI_V26);\n  await fs.promises.mkdir(anyDpiV26Directory, { recursive: true });\n  const launcherPath = path.resolve(anyDpiV26Directory, IC_LAUNCHER_XML);\n  const launcherRoundPath = path.resolve(anyDpiV26Directory, IC_LAUNCHER_ROUND_XML);\n  if (add) {\n    await Promise.all([\n      fs.promises.writeFile(launcherPath, icLauncherXmlString, 'utf8'),\n      fs.promises.writeFile(launcherRoundPath, icLauncherXmlString, 'utf8'),\n    ]);\n  } else {\n    // Remove the xml if the icon switches from adaptive to standard.\n    await Promise.all(\n      [launcherPath, launcherRoundPath].map(async (path) => {\n        return fs.promises.rm(path, { force: true });\n      })\n    );\n  }\n}\n\nasync function generateMultiLayerImageAsync(\n  projectRoot: string,\n  {\n    icon,\n    backgroundColor,\n    backgroundImage,\n    imageCacheFolder,\n    backgroundImageCacheFolder,\n    borderRadiusRatio,\n    outputImageFileName,\n    backgroundImageFileName,\n    isAdaptive,\n  }: {\n    icon: string;\n    backgroundImage: string | null;\n    backgroundColor: string | null;\n    imageCacheFolder: string;\n    backgroundImageCacheFolder: string;\n    backgroundImageFileName?: string;\n    borderRadiusRatio?: number;\n    outputImageFileName: string;\n    isAdaptive?: boolean;\n  }\n) {\n  await iterateDpiValues(projectRoot, async ({ dpiFolder, scale }) => {\n    let iconLayer = await generateIconAsync(projectRoot, {\n      cacheType: imageCacheFolder,\n      src: icon,\n      scale,\n      // backgroundImage overrides backgroundColor\n      backgroundColor: backgroundImage ? 'transparent' : (backgroundColor ?? 'transparent'),\n      borderRadiusRatio,\n      isAdaptive,\n    });\n\n    if (backgroundImage) {\n      const backgroundLayer = await generateIconAsync(projectRoot, {\n        cacheType: backgroundImageCacheFolder,\n        src: backgroundImage,\n        scale,\n        backgroundColor: 'transparent',\n        borderRadiusRatio,\n        isAdaptive,\n      });\n\n      if (backgroundImageFileName) {\n        await fs.promises.writeFile(\n          path.resolve(dpiFolder, backgroundImageFileName),\n          backgroundLayer\n        );\n      } else {\n        iconLayer = await compositeImagesAsync({\n          foreground: iconLayer,\n          background: backgroundLayer,\n        });\n      }\n    } else if (backgroundImageFileName) {\n      // Remove any instances of ic_launcher_background.png that are there from previous icons\n      await deleteIconNamedAsync(projectRoot, backgroundImageFileName);\n    }\n\n    await fs.promises.mkdir(dpiFolder, { recursive: true });\n    await fs.promises.writeFile(path.resolve(dpiFolder, outputImageFileName), iconLayer);\n  });\n}\n\nasync function generateMonochromeImageAsync(\n  projectRoot: string,\n  {\n    icon,\n    imageCacheFolder,\n    outputImageFileName,\n  }: { icon: string; imageCacheFolder: string; outputImageFileName: string }\n) {\n  await iterateDpiValues(projectRoot, async ({ dpiFolder, scale }) => {\n    const monochromeIcon = await generateIconAsync(projectRoot, {\n      cacheType: imageCacheFolder,\n      src: icon,\n      scale,\n      backgroundColor: 'transparent',\n      isAdaptive: true,\n    });\n    await fs.promises.mkdir(dpiFolder, { recursive: true });\n    await fs.promises.writeFile(path.resolve(dpiFolder, outputImageFileName), monochromeIcon);\n  });\n}\n\nfunction iterateDpiValues(\n  projectRoot: string,\n  callback: (value: { dpiFolder: string; folderName: string; scale: number }) => Promise<void>\n) {\n  return Promise.all(\n    Object.values(dpiValues).map((value) =>\n      callback({\n        dpiFolder: path.resolve(projectRoot, ANDROID_RES_PATH, value.folderName),\n        ...value,\n      })\n    )\n  );\n}\n\nasync function deleteIconNamedAsync(projectRoot: string, name: string) {\n  return iterateDpiValues(projectRoot, ({ dpiFolder }) => {\n    return fs.promises.rm(path.resolve(dpiFolder, name), { force: true });\n  });\n}\n\nasync function generateIconAsync(\n  projectRoot: string,\n  {\n    cacheType,\n    src,\n    scale,\n    backgroundColor,\n    borderRadiusRatio,\n    isAdaptive,\n  }: {\n    cacheType: string;\n    src: string;\n    scale: number;\n    backgroundColor: string;\n    borderRadiusRatio?: number;\n    isAdaptive?: boolean;\n  }\n) {\n  const iconSizePx =\n    (isAdaptive ? ADAPTIVE_BASELINE_PIXEL_SIZE : LEGACY_BASELINE_PIXEL_SIZE) * scale;\n\n  return (\n    await generateImageAsync(\n      { projectRoot, cacheType },\n      {\n        src,\n        width: iconSizePx,\n        height: iconSizePx,\n        resizeMode: 'cover',\n        backgroundColor,\n        borderRadius: borderRadiusRatio ? iconSizePx * borderRadiusRatio : undefined,\n      }\n    )\n  ).source;\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,0BAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,yBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsE,SAAAI,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEtE,MAAM;EAAEG;AAAO,CAAC,GAAGC,8BAAa;AAKzB,MAAMC,SAAiB,GAAAC,OAAA,CAAAD,SAAA,GAAG;EAC/BE,IAAI,EAAE;IAAEC,UAAU,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAE,CAAC;EAC7CC,IAAI,EAAE;IAAEF,UAAU,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAI,CAAC;EAC/CE,KAAK,EAAE;IAAEH,UAAU,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAE,CAAC;EAC/CG,MAAM,EAAE;IAAEJ,UAAU,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAE,CAAC;EACjDI,OAAO,EAAE;IAAEL,UAAU,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAE;AACpD,CAAC;AAED,MAAMK,0BAA0B,GAAG,EAAE;AACrC,MAAMC,4BAA4B,GAAG,GAAG;AAEjC,MAAMC,gBAAgB,GAAAV,OAAA,CAAAU,gBAAA,GAAG,2BAA2B;AAC3D,MAAMC,iBAAiB,GAAG,mBAAmB;AAC7C,MAAMC,eAAe,GAAG,gBAAgB;AACxC,MAAMC,gBAAgB,GAAG,kBAAkB;AAC3C,MAAMC,sBAAsB,GAAG,wBAAwB;AACvD,MAAMC,2BAA2B,GAAG,6BAA6B;AACjE,MAAMC,2BAA2B,GAAG,6BAA6B;AACjE,MAAMC,2BAA2B,GAAG,6BAA6B;AACjE,MAAMC,eAAe,GAAG,iBAAiB;AACzC,MAAMC,qBAAqB,GAAG,uBAAuB;AAE9C,MAAMC,gBAA8B,GAAIC,MAAM,IAAK;EACxD,MAAM;IAAEC,eAAe;IAAEC,eAAe;IAAEC,eAAe;IAAEC;EAAgB,CAAC,GAC1EC,eAAe,CAACL,MAAM,CAAC;EACzB,MAAMM,IAAI,GAAGL,eAAe,IAAIM,OAAO,CAACP,MAAM,CAAC;EAE/C,IAAI,CAACM,IAAI,EAAE;IACT,OAAON,MAAM;EACf;EAEAA,MAAM,GAAG,IAAAQ,oDAAwB,EAACR,MAAM,CAAC;EACzC;EACAA,MAAM,GAAGS,6BAA6B,CAACT,MAAM,EAAEE,eAAe,CAAC;EAC/D,OAAO,IAAAQ,iCAAgB,EAACV,MAAM,EAAE,CAC9B,SAAS,EACT,MAAOA,MAAM,IAAK;IAChB,MAAMW,YAAY,CAACX,MAAM,CAACY,UAAU,CAACC,WAAW,EAAE;MAChDP,IAAI;MACJJ,eAAe;MACfC,eAAe;MACfC,eAAe;MACfU,UAAU,EAAE,CAAC,CAACd,MAAM,CAACe,OAAO,EAAEC;IAChC,CAAC,CAAC;IACF,OAAOhB,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACrB,OAAA,CAAAoB,gBAAA,GAAAA,gBAAA;AAEK,SAASkB,oBAAoBA,CAClCjB,MAAmC,EACnCkB,QAAgD,EACR;EACxC,MAAMJ,UAAU,GAAG,CAAC,CAACd,MAAM,CAACe,OAAO,EAAEC,YAAY;EACjD,MAAMG,WAAW,GAAG1C,8BAAa,CAAC2C,QAAQ,CAACC,yBAAyB,CAACH,QAAQ,CAAC;EAE9E,IAAIJ,UAAU,EAAE;IACdK,WAAW,CAACG,CAAC,CAAC,mBAAmB,CAAC,GAAG,2BAA2B;EAClE,CAAC,MAAM;IACL,OAAOH,WAAW,CAACG,CAAC,CAAC,mBAAmB,CAAC;EAC3C;EACA,OAAOJ,QAAQ;AACjB;AAEA,MAAMT,6BAA0D,GAAGA,CAACT,MAAM,EAAEE,eAAe,KAAK;EAC9F,OAAO,IAAAqB,kCAAiB,EAACvB,MAAM,EAAGA,MAAM,IAAK;IAC3CA,MAAM,CAACwB,UAAU,GAAGC,kBAAkB,CAACvB,eAAe,IAAI,SAAS,EAAEF,MAAM,CAACwB,UAAU,CAAC;IACvF,OAAOxB,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAEM,SAASO,OAAOA,CAACP,MAAkB,EAAE;EAC1C,OAAOA,MAAM,CAACe,OAAO,EAAET,IAAI,IAAIN,MAAM,CAACM,IAAI,IAAI,IAAI;AACpD;AAEO,SAASD,eAAeA,CAACL,MAAkB,EAAE;EAClD,OAAO;IACLC,eAAe,EAAED,MAAM,CAACe,OAAO,EAAEC,YAAY,EAAEf,eAAe,IAAI,IAAI;IACtEC,eAAe,EAAEF,MAAM,CAACe,OAAO,EAAEC,YAAY,EAAEd,eAAe,IAAI,IAAI;IACtEC,eAAe,EAAEH,MAAM,CAACe,OAAO,EAAEC,YAAY,EAAEb,eAAe,IAAI,IAAI;IACtEC,eAAe,EAAEJ,MAAM,CAACe,OAAO,EAAEC,YAAY,EAAEZ,eAAe,IAAI;EACpE,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACO,eAAeO,YAAYA,CAChCE,WAAmB,EACnB;EACEP,IAAI;EACJJ,eAAe;EACfC,eAAe;EACfC,eAAe;EACfU;AAOF,CAAC,EACD;EACA,IAAI,CAACR,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EAEA,MAAMoB,wBAAwB,CAACb,WAAW,EAAEP,IAAI,EAAEH,eAAe,EAAED,eAAe,CAAC;EACnF,IAAIY,UAAU,EAAE;IACd,MAAMa,sBAAsB,CAACd,WAAW,EAAEP,IAAI,EAAEH,eAAe,EAAED,eAAe,CAAC;EACnF,CAAC,MAAM;IACL,MAAM0B,oBAAoB,CAACf,WAAW,EAAEpB,sBAAsB,CAAC;EACjE;EACA,MAAMoC,0BAA0B,CAAChB,WAAW,EAAEP,IAAI,EAAEH,eAAe,EAAEC,eAAe,EAAEU,UAAU,CAAC;EAEjG,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeY,wBAAwBA,CACrCb,WAAmB,EACnBP,IAAY,EACZH,eAA8B,EAC9BD,eAA8B,EAC9B;EACA,OAAO4B,4BAA4B,CAACjB,WAAW,EAAE;IAC/CP,IAAI;IACJH,eAAe;IACfD,eAAe;IACf6B,mBAAmB,EAAEvC,gBAAgB;IACrCwC,gBAAgB,EAAE,yBAAyB;IAC3CC,0BAA0B,EAAE;EAC9B,CAAC,CAAC;AACJ;AAEA,eAAeN,sBAAsBA,CACnCd,WAAmB,EACnBP,IAAY,EACZH,eAA8B,EAC9BD,eAA8B,EAC9B;EACA,OAAO4B,4BAA4B,CAACjB,WAAW,EAAE;IAC/CP,IAAI;IACJ4B,iBAAiB,EAAE,GAAG;IACtBH,mBAAmB,EAAEtC,sBAAsB;IAC3CU,eAAe;IACfD,eAAe;IACf8B,gBAAgB,EAAE,yBAAyB;IAC3CC,0BAA0B,EAAE,mCAAmC;IAC/DnB,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,eAAee,0BAA0BA,CAC9ChB,WAAmB,EACnBZ,eAAuB,EACvBE,eAA8B,EAC9BC,eAA8B,EAC9BU,UAAmB,EACnB;EACA,IAAIV,eAAe,EAAE;IACnB,MAAM+B,4BAA4B,CAACtB,WAAW,EAAE;MAC9CP,IAAI,EAAEF,eAAe;MACrB4B,gBAAgB,EAAE,6BAA6B;MAC/CD,mBAAmB,EAAEnC;IACvB,CAAC,CAAC;EACJ;EACA,MAAMkC,4BAA4B,CAACjB,WAAW,EAAE;IAC9CX,eAAe,EAAE,aAAa;IAC9BC,eAAe;IACf8B,0BAA0B,EAAE,6BAA6B;IACzDF,mBAAmB,EAAEpC,2BAA2B;IAChDW,IAAI,EAAEL,eAAe;IACrB+B,gBAAgB,EAAE,6BAA6B;IAC/CI,uBAAuB,EAAE1C,2BAA2B;IACpDoB,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAMuB,mBAAmB,GAAGC,2BAA2B,CAACnC,eAAe,EAAEC,eAAe,CAAC;EACzF,MAAMmC,0BAA0B,CAC9B1B,WAAW,EACXwB,mBAAmB;EACnB;EACA;EACAvB,UACF,CAAC;AACH;AAEA,SAASW,kBAAkBA,CAACvB,eAA8B,EAAEsC,MAAmB,EAAE;EAC/E,OAAOhE,MAAM,CAACiE,gBAAgB,CAACD,MAAM,EAAE;IACrCE,KAAK,EAAExC,eAAe;IACtByC,IAAI,EAAEpD;EACR,CAAC,CAAC;AACJ;AAEO,MAAM+C,2BAA2B,GAAGA,CACzCnC,eAA8B,EAC9BC,eAA8B,KAC3B;EACH,MAAMwC,UAAU,GAAGzC,eAAe,GAAG,gCAAgC,GAAG,uBAAuB;EAE/F,MAAM0C,YAAsB,GAAG,CAC7B,iCAAiCD,UAAU,KAAK,EAChD,iEAAiE,CAClE;EAED,IAAIxC,eAAe,EAAE;IACnByC,YAAY,CAACC,IAAI,CAAC,iEAAiE,CAAC;EACtF;EAEA,OAAO;AACT;AACA,MAAMD,YAAY,CAACE,IAAI,CAAC,QAAQ,CAAC;AACjC,iBAAiB;AACjB,CAAC;AAACpE,OAAA,CAAA2D,2BAAA,GAAAA,2BAAA;AAEF,eAAeC,0BAA0BA,CACvC1B,WAAmB,EACnBwB,mBAA2B,EAC3BW,GAAY,EACZ;EACA,MAAMC,kBAAkB,GAAGC,eAAI,CAACC,OAAO,CAACtC,WAAW,EAAExB,gBAAgB,EAAEC,iBAAiB,CAAC;EACzF,MAAM8D,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACL,kBAAkB,EAAE;IAAEM,SAAS,EAAE;EAAK,CAAC,CAAC;EAChE,MAAMC,YAAY,GAAGN,eAAI,CAACC,OAAO,CAACF,kBAAkB,EAAEpD,eAAe,CAAC;EACtE,MAAM4D,iBAAiB,GAAGP,eAAI,CAACC,OAAO,CAACF,kBAAkB,EAAEnD,qBAAqB,CAAC;EACjF,IAAIkD,GAAG,EAAE;IACP,MAAMU,OAAO,CAACC,GAAG,CAAC,CAChBP,aAAE,CAACC,QAAQ,CAACO,SAAS,CAACJ,YAAY,EAAEnB,mBAAmB,EAAE,MAAM,CAAC,EAChEe,aAAE,CAACC,QAAQ,CAACO,SAAS,CAACH,iBAAiB,EAAEpB,mBAAmB,EAAE,MAAM,CAAC,CACtE,CAAC;EACJ,CAAC,MAAM;IACL;IACA,MAAMqB,OAAO,CAACC,GAAG,CACf,CAACH,YAAY,EAAEC,iBAAiB,CAAC,CAACI,GAAG,CAAC,MAAOX,IAAI,IAAK;MACpD,OAAOE,aAAE,CAACC,QAAQ,CAACS,EAAE,CAACZ,IAAI,EAAE;QAAEa,KAAK,EAAE;MAAK,CAAC,CAAC;IAC9C,CAAC,CACH,CAAC;EACH;AACF;AAEA,eAAejC,4BAA4BA,CACzCjB,WAAmB,EACnB;EACEP,IAAI;EACJJ,eAAe;EACfC,eAAe;EACf6B,gBAAgB;EAChBC,0BAA0B;EAC1BC,iBAAiB;EACjBH,mBAAmB;EACnBK,uBAAuB;EACvBtB;AAWF,CAAC,EACD;EACA,MAAMkD,gBAAgB,CAACnD,WAAW,EAAE,OAAO;IAAEoD,SAAS;IAAEnF;EAAM,CAAC,KAAK;IAClE,IAAIoF,SAAS,GAAG,MAAMC,iBAAiB,CAACtD,WAAW,EAAE;MACnDuD,SAAS,EAAEpC,gBAAgB;MAC3BqC,GAAG,EAAE/D,IAAI;MACTxB,KAAK;MACL;MACAoB,eAAe,EAAEC,eAAe,GAAG,aAAa,GAAID,eAAe,IAAI,aAAc;MACrFgC,iBAAiB;MACjBpB;IACF,CAAC,CAAC;IAEF,IAAIX,eAAe,EAAE;MACnB,MAAMmE,eAAe,GAAG,MAAMH,iBAAiB,CAACtD,WAAW,EAAE;QAC3DuD,SAAS,EAAEnC,0BAA0B;QACrCoC,GAAG,EAAElE,eAAe;QACpBrB,KAAK;QACLoB,eAAe,EAAE,aAAa;QAC9BgC,iBAAiB;QACjBpB;MACF,CAAC,CAAC;MAEF,IAAIsB,uBAAuB,EAAE;QAC3B,MAAMgB,aAAE,CAACC,QAAQ,CAACO,SAAS,CACzBV,eAAI,CAACC,OAAO,CAACc,SAAS,EAAE7B,uBAAuB,CAAC,EAChDkC,eACF,CAAC;MACH,CAAC,MAAM;QACLJ,SAAS,GAAG,MAAM,IAAAK,kCAAoB,EAAC;UACrCC,UAAU,EAAEN,SAAS;UACrBtB,UAAU,EAAE0B;QACd,CAAC,CAAC;MACJ;IACF,CAAC,MAAM,IAAIlC,uBAAuB,EAAE;MAClC;MACA,MAAMR,oBAAoB,CAACf,WAAW,EAAEuB,uBAAuB,CAAC;IAClE;IAEA,MAAMgB,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACW,SAAS,EAAE;MAAEV,SAAS,EAAE;IAAK,CAAC,CAAC;IACvD,MAAMH,aAAE,CAACC,QAAQ,CAACO,SAAS,CAACV,eAAI,CAACC,OAAO,CAACc,SAAS,EAAElC,mBAAmB,CAAC,EAAEmC,SAAS,CAAC;EACtF,CAAC,CAAC;AACJ;AAEA,eAAe/B,4BAA4BA,CACzCtB,WAAmB,EACnB;EACEP,IAAI;EACJ0B,gBAAgB;EAChBD;AACuE,CAAC,EAC1E;EACA,MAAMiC,gBAAgB,CAACnD,WAAW,EAAE,OAAO;IAAEoD,SAAS;IAAEnF;EAAM,CAAC,KAAK;IAClE,MAAM2F,cAAc,GAAG,MAAMN,iBAAiB,CAACtD,WAAW,EAAE;MAC1DuD,SAAS,EAAEpC,gBAAgB;MAC3BqC,GAAG,EAAE/D,IAAI;MACTxB,KAAK;MACLoB,eAAe,EAAE,aAAa;MAC9BY,UAAU,EAAE;IACd,CAAC,CAAC;IACF,MAAMsC,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACW,SAAS,EAAE;MAAEV,SAAS,EAAE;IAAK,CAAC,CAAC;IACvD,MAAMH,aAAE,CAACC,QAAQ,CAACO,SAAS,CAACV,eAAI,CAACC,OAAO,CAACc,SAAS,EAAElC,mBAAmB,CAAC,EAAE0C,cAAc,CAAC;EAC3F,CAAC,CAAC;AACJ;AAEA,SAAST,gBAAgBA,CACvBnD,WAAmB,EACnB6D,QAA4F,EAC5F;EACA,OAAOhB,OAAO,CAACC,GAAG,CAChBgB,MAAM,CAACC,MAAM,CAAClG,SAAS,CAAC,CAACmF,GAAG,CAAEnB,KAAK,IACjCgC,QAAQ,CAAC;IACPT,SAAS,EAAEf,eAAI,CAACC,OAAO,CAACtC,WAAW,EAAExB,gBAAgB,EAAEqD,KAAK,CAAC7D,UAAU,CAAC;IACxE,GAAG6D;EACL,CAAC,CACH,CACF,CAAC;AACH;AAEA,eAAed,oBAAoBA,CAACf,WAAmB,EAAE8B,IAAY,EAAE;EACrE,OAAOqB,gBAAgB,CAACnD,WAAW,EAAE,CAAC;IAAEoD;EAAU,CAAC,KAAK;IACtD,OAAOb,aAAE,CAACC,QAAQ,CAACS,EAAE,CAACZ,eAAI,CAACC,OAAO,CAACc,SAAS,EAAEtB,IAAI,CAAC,EAAE;MAAEoB,KAAK,EAAE;IAAK,CAAC,CAAC;EACvE,CAAC,CAAC;AACJ;AAEA,eAAeI,iBAAiBA,CAC9BtD,WAAmB,EACnB;EACEuD,SAAS;EACTC,GAAG;EACHvF,KAAK;EACLoB,eAAe;EACfgC,iBAAiB;EACjBpB;AAQF,CAAC,EACD;EACA,MAAM+D,UAAU,GACd,CAAC/D,UAAU,GAAG1B,4BAA4B,GAAGD,0BAA0B,IAAIL,KAAK;EAElF,OAAO,CACL,MAAM,IAAAgG,gCAAkB,EACtB;IAAEjE,WAAW;IAAEuD;EAAU,CAAC,EAC1B;IACEC,GAAG;IACHU,KAAK,EAAEF,UAAU;IACjBG,MAAM,EAAEH,UAAU;IAClBI,UAAU,EAAE,OAAO;IACnB/E,eAAe;IACfgF,YAAY,EAAEhD,iBAAiB,GAAG2C,UAAU,GAAG3C,iBAAiB,GAAGiD;EACrE,CACF,CAAC,EACDC,MAAM;AACV", "ignoreList": []}