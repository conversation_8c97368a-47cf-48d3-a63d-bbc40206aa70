import*as e from"../../core/common/common.js";import*as r from"../../core/i18n/i18n.js";import*as o from"../../core/sdk/sdk.js";import*as s from"../../ui/legacy/legacy.js";const i={developerResources:"Developer resources",showDeveloperResources:"Show Developer resources"},a=r.i18n.registerUIStrings("panels/developer_resources/developer_resources-meta.ts",i),c=r.i18n.getLazilyComputedLocalizedString.bind(void 0,a);let t;async function l(){return t||(t=await import("./developer_resources.js")),t}s.ViewManager.registerViewExtension({location:"drawer-view",id:"developer-resources",title:c(i.developerResources),commandPrompt:c(i.showDeveloperResources),order:100,persistence:"closeable",loadView:async()=>new((await l()).DeveloperResourcesView.DeveloperResourcesView)}),e.Revealer.registerRevealer({contextTypes:()=>[o.PageResourceLoader.ResourceKey],destination:e.Revealer.RevealerDestination.DEVELOPER_RESOURCES_PANEL,loadRevealer:async()=>new((await l()).DeveloperResourcesView.DeveloperResourcesRevealer)});
