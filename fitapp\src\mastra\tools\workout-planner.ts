import { createTool } from '@mastra/core';
import { WorkoutRequestSchema } from '../schemas/user-profile.js';
import { workoutDatabase } from '../utils/workout-data.js';
import type { WorkoutPlan } from '../schemas/fitness-data.js';

export const workoutPlannerTool = createTool({
  id: 'workout-planner',
  description: 'Kullanıcının profili, BMI kategorisi ve fitness seviyesine göre kişiselleştirilmiş antrenman programı oluşturur',
  inputSchema: WorkoutRequestSchema,
  execute: async ({ context }) => {
    const { userProfile, bmiCategory, fitnessLevel, availableTime, preferredWorkoutType } = context;
    
    try {
      // BMI kategorisine göre antrenman önerilerini al
      const bmiWorkoutData = workoutDatabase.workoutsByBMI[bmiCategory];
      
      // Kullanıcının hedefine göre program seç
      const primaryGoal = userProfile.fitnessGoals?.[0] || 'genel_sağlık';
      let weeklyTemplate = workoutDatabase.weeklyPrograms.genel_sağlık;
      
      if (primaryGoal === 'kilo_verme') {
        weeklyTemplate = workoutDatabase.weeklyPrograms.kilo_verme;
      } else if (primaryGoal === 'kas_kazanma') {
        weeklyTemplate = workoutDatabase.weeklyPrograms.kas_kazanma;
      }
      
      // Haftalık programı oluştur
      const weeklySchedule = [];
      const days = ['pazartesi', 'salı', 'çarşamba', 'perşembe', 'cuma', 'cumartesi', 'pazar'];
      
      for (const day of days) {
        const dayProgram = weeklyTemplate[day as keyof typeof weeklyTemplate];
        
        if (dayProgram.type === 'dinlenme') {
          weeklySchedule.push({
            day: day.charAt(0).toUpperCase() + day.slice(1),
            workoutType: 'Dinlenme Günü',
            duration: 0,
            exercises: [{
              name: 'Aktif Dinlenme',
              description: 'Hafif yürüyüş, germe egzersizleri veya tamamen dinlenme'
            }]
          });
          continue;
        }
        
        // Kullanıcının mevcut zamanına göre süreyi ayarla
        let adjustedDuration = Math.min(dayProgram.duration, availableTime);
        
        // Tercih edilen antrenman tipini dikkate al
        let workoutType = dayProgram.type;
        if (preferredWorkoutType && 
            (dayProgram.type === 'karma' || dayProgram.type === 'kardiyovasküler')) {
          workoutType = preferredWorkoutType;
        }
        
        // Fitness seviyesine göre egzersizleri seç
        let exercises = [];
        
        if (workoutType === 'kardiyovasküler') {
          exercises = workoutDatabase.exercises.kardiyovasküler[fitnessLevel] || 
                     workoutDatabase.exercises.kardiyovasküler.başlangıç;
        } else if (workoutType === 'güç_antrenmanı') {
          exercises = workoutDatabase.exercises.güç_antrenmanı[fitnessLevel] || 
                     workoutDatabase.exercises.güç_antrenmanı.başlangıç;
        } else if (workoutType === 'yoga' || workoutType === 'pilates') {
          exercises = workoutDatabase.exercises.yoga[fitnessLevel] || 
                     workoutDatabase.exercises.yoga.başlangıç;
        } else {
          // Karma antrenman için kardiyovasküler ve güç antrenmanını birleştir
          const cardioExercises = workoutDatabase.exercises.kardiyovasküler[fitnessLevel]?.slice(0, 1) || [];
          const strengthExercises = workoutDatabase.exercises.güç_antrenmanı[fitnessLevel]?.slice(0, 2) || [];
          exercises = [...cardioExercises, ...strengthExercises];
        }
        
        // BMI kategorisine göre özel ayarlamalar
        if (bmiCategory === 'obez_1' || bmiCategory === 'obez_2' || bmiCategory === 'obez_3') {
          // Obez kategoriler için düşük etkili egzersizler
          exercises = exercises.map(exercise => ({
            ...exercise,
            description: exercise.description + ' (Düşük yoğunlukta başlayın)'
          }));
          adjustedDuration = Math.min(adjustedDuration, 30); // Maksimum 30 dakika
        }
        
        weeklySchedule.push({
          day: day.charAt(0).toUpperCase() + day.slice(1),
          workoutType: getWorkoutTypeDisplayName(workoutType),
          duration: adjustedDuration,
          exercises: exercises.slice(0, 4) // Maksimum 4 egzersiz
        });
      }
      
      // İlerleme ipuçları
      const progressionTips = [
        'İlk 2 hafta mevcut programı takip edin',
        'Vücudunuz alıştıkça yoğunluğu kademeli olarak artırın',
        'Her hafta egzersiz süresini 5-10 dakika artırabilirsiniz',
        'Ağırlık antrenmanında her 2 haftada bir ağırlığı %5-10 artırın',
        'İlerlemenizi takip etmek için günlük tutun'
      ];
      
      // BMI kategorisine göre özel ipuçları ekle
      if (bmiCategory === 'zayıf') {
        progressionTips.push('Kas kütlesi kazanmak için protein alımınızı artırın');
        progressionTips.push('Ağırlık antrenmanına odaklanın');
      } else if (bmiCategory === 'fazla_kilolu' || bmiCategory.startsWith('obez')) {
        progressionTips.push('Kilo verme için kardiyovasküler egzersize öncelik verin');
        progressionTips.push('Eklem sağlığınızı korumak için düşük etkili egzersizler seçin');
      }
      
      // Gerekli ekipmanlar
      const equipmentNeeded = [];
      if (weeklySchedule.some(day => day.workoutType.includes('Güç'))) {
        equipmentNeeded.push('Dumbbell seti', 'Yoga matı', 'Direnç bandı');
      }
      if (weeklySchedule.some(day => day.workoutType.includes('Kardiyovasküler'))) {
        equipmentNeeded.push('Koşu ayakkabısı', 'Su şişesi');
      }
      if (weeklySchedule.some(day => day.workoutType.includes('Yoga'))) {
        equipmentNeeded.push('Yoga matı', 'Yoga bloğu', 'Yoga kayışı');
      }
      
      const workoutPlan: WorkoutPlan = {
        weeklySchedule,
        progressionTips,
        safetyGuidelines: workoutDatabase.safetyGuidelines,
        equipmentNeeded: [...new Set(equipmentNeeded)] // Tekrarları kaldır
      };
      
      return {
        success: true,
        data: workoutPlan,
        message: `${userProfile.name} için ${bmiCategory} kategorisinde, ${fitnessLevel} seviyesinde kişiselleştirilmiş antrenman programı hazırlandı.`
      };
      
    } catch (error) {
      return {
        success: false,
        error: 'Antrenman planı oluşturulurken bir hata oluştu',
        details: error instanceof Error ? error.message : 'Bilinmeyen hata'
      };
    }
  }
});

function getWorkoutTypeDisplayName(type: string): string {
  const displayNames: Record<string, string> = {
    'kardiyovasküler': 'Kardiyovasküler Antrenman',
    'güç_antrenmanı': 'Güç Antrenmanı',
    'karma': 'Karma Antrenman',
    'yoga': 'Yoga',
    'pilates': 'Pilates',
    'dinlenme': 'Dinlenme Günü'
  };
  
  return displayNames[type] || type;
}
