import*as t from"../../../core/platform/platform.js";import*as e from"../helpers/helpers.js";import*as n from"../types/types.js";import"../handlers/handlers.js";const r=[{flag:8192,failure:"UNSUPPORTED_CSS_PROPERTY"},{flag:2048,failure:"TRANSFROM_BOX_SIZE_DEPENDENT"},{flag:4096,failure:"FILTER_MAY_MOVE_PIXELS"},{flag:16,failure:"NON_REPLACE_COMPOSITE_MODE"},{flag:64,failure:"INCOMPATIBLE_ANIMATIONS"},{flag:8,failure:"UNSUPPORTED_TIMING_PARAMS"}],i=e.Timing.secondsToMicroseconds(n.Timing.Seconds(.5));function o(t,e){const n=t.dur?t.ts+t.dur:t.ts;return n<e.ts&&n>=e.ts-i}function a(e,n){const r=t.ArrayUtilities.nearestIndexFromBeginning(e,(t=>t.ts>n.ts+(n.dur||0)));if(null!==r)return e[r]}var s=Object.freeze({__proto__:null,deps:function(){return["Meta","Animations","LayoutShifts","NetworkRequests"]},generateInsight:function(n,i){const s=t=>{const r=e.Trace.getNavigationForTraceEvent(t,i.frameId,n.Meta.navigationsByFrameId);return r?.args.data?.navigationId===i.navigationId},c=function(t){const e=[];for(const n of t){const t=n.args.data.beginEvent,i=n.args.data.instantEvents||[];for(const n of i){const i=n.args.data.compositeFailed,o=n.args.data.unsupportedProperties;if(!i||!o)continue;const a=r.filter((t=>i&t.flag)).map((t=>t.failure)),s={name:t.args.data.displayName,failureReasons:a,unsupportedProperties:o};e.push(s)}}return e}(n.Animations.animations.filter(s)),d=n.LayoutShifts.renderFrameImplCreateChildFrameEvents.filter(s),u=n.NetworkRequests.byTime.filter(s),g=n.LayoutShifts.clusters.flatMap((t=>s(t.events[0])?t.events:[])),f=n.LayoutShifts.prePaintEvents.filter(s),l=new Map,m=function(e,n){const r=new Map;for(const i of n){const n=t.ArrayUtilities.nearestIndexFromBeginning(e,(t=>t.ts>=i.ts));if(null!==n)for(let o=n;o<e.length;o++){const n=e[o];if(n.ts>=i.ts&&n.ts<=i.ts+i.dur&&t.MapUtilities.getWithDefault(r,i,(()=>[])).push(n),n.ts>i.ts+i.dur)break}}return r}(g,f);for(const t of g)l.set(t,{iframes:[],fontRequests:[]});return function(e,n,r,i){for(const o of e){const e=a(n,o);if(!e)continue;const s=r.get(e);if(s)for(const e of s)t.MapUtilities.getWithDefault(i,e,(()=>({iframes:[],fontRequests:[]}))).iframes.push(o)}}(d,f,m,l),function(e,n,r,i){const s=e.filter((t=>"Font"===t.args.data.resourceType&&t.args.data.mimeType.startsWith("font")));for(const e of s){const s=a(n,e);if(!s)continue;if(!o(e,s))continue;const c=r.get(s);if(c)for(const n of c)t.MapUtilities.getWithDefault(i,n,(()=>({iframes:[],fontRequests:[]}))).fontRequests.push(e)}}(u,f,m,l),{animationFailures:c,shifts:l}}});var c=Object.freeze({__proto__:null,deps:function(){return["Meta","NetworkRequests"]},generateInsight:function(t,e){const r=t.NetworkRequests.byTime.find((t=>t.args.data.requestId===e.navigationId));if(!r)throw new Error("missing document request");const i=function(t){const e=t.args.data.timing;return e?n.Timing.MilliSeconds(Math.round(e.receiveHeadersStart-e.sendEnd)):null}(r);if(null===i)throw new Error("missing document request timing");let o=0;i>600&&(o=Math.max(i-100,0));const a=Math.round(r.args.data.syntheticData.redirectionDuration/1e3);o+=a;const s={FCP:o,LCP:o};return{serverResponseTime:i,redirectDuration:n.Timing.MilliSeconds(a),metricSavings:s}}});var d,u=Object.freeze({__proto__:null,deps:function(){return["UserInteractions"]},generateInsight:function(t,e){const n=t.UserInteractions.interactionEvents.filter((t=>t.args.data.navigationId===e.navigationId));if(!n.length)return{};const r=new Map;for(const t of n){const e=t.interactionId,n=r.get(e);(!n||t.dur>n.dur)&&r.set(e,t)}const i=[...r.values()];i.sort(((t,e)=>e.dur-t.dur));const o=Math.min(9,Math.floor(i.length/50));return{longestInteractionEvent:i[0],highPercentileInteractionEvent:i[o]}}});!function(t){t.NO_FP="NO_FP",t.NO_LCP="NO_LCP",t.NO_DOCUMENT_REQUEST="NO_DOCUMENT_REQUEST",t.NO_LAYOUT="NO_LAYOUT"}(d||(d={}));var g=Object.freeze({__proto__:null,get InsightWarning(){return d}});function f(t,r,i,o){const a=r.args.data.timing;if(!a)throw new Error("no timing for main resource");const s=e.Timing.secondsToMicroseconds(a.requestTime)+e.Timing.millisecondsToMicroseconds(a.receiveHeadersStart),c=n.Timing.MicroSeconds(s-t.ts),d=e.Timing.microSecondsToMilliseconds(c);let u=n.Timing.MilliSeconds(i-d);if(!o)return{ttfb:d,renderDelay:u};const g=n.Timing.MicroSeconds(o.ts-t.ts),f=e.Timing.microSecondsToMilliseconds(g),l=n.Timing.MicroSeconds(o.args.data.syntheticData.finishTime-t.ts),m=e.Timing.microSecondsToMilliseconds(l),p=n.Timing.MilliSeconds(f-d),T=n.Timing.MilliSeconds(m-f);return u=n.Timing.MilliSeconds(i-m),{ttfb:d,loadDelay:p,loadTime:T,renderDelay:u}}var l=Object.freeze({__proto__:null,deps:function(){return["NetworkRequests","PageLoadMetrics","LargestImagePaint","Meta"]},generateInsight:function(t,r){const i=t.NetworkRequests,o=t.Meta.navigationsByNavigationId.get(r.navigationId);if(!o)throw new Error("no trace navigation");const a=t.PageLoadMetrics.metricScoresByFrameId.get(r.frameId);if(!a)throw new Error("no frame metrics");const s=a.get(r.navigationId);if(!s)throw new Error("no navigation metrics");const c=s.get("LCP"),u=c?.event;if(!u||!n.TraceEvents.isTraceEventLargestContentfulPaintCandidate(u))return{warnings:[d.NO_LCP]};const g=e.Timing.microSecondsToMilliseconds(c.timing),l=c.event?.ts?e.Timing.microSecondsToMilliseconds(c.event?.ts):void 0,m=function(t,n,r){const i=r.args.data?.nodeId;if(!i)throw new Error("no lcp node id");const o=t.LargestImagePaint.get(i);if(!o)return null;const a=o.args.data?.imageUrl;if(!a)throw new Error("no lcp url");const s=t.NetworkRequests.byTime.find((r=>{const i=e.Trace.getNavigationForTraceEvent(r,n.frameId,t.Meta.navigationsByFrameId);return i?.args.data?.navigationId===n.navigationId&&r.args.data.url===a}));if(!s)throw new Error("no lcp resource found");return s}(t,r,u),p=i.byTime.find((t=>t.args.data.requestId===r.navigationId));if(!p)return{lcpMs:g,lcpTs:l,warnings:[d.NO_DOCUMENT_REQUEST]};if(!m)return{lcpMs:g,lcpTs:l,phases:f(o,p,g,m)};const T=u.args.data?.loadingAttr,h=m?.args.data.isLinkPreload||"preload"===m?.args.data.initiator?.type,I=m?.args.data.fetchPriorityHint;return{lcpMs:g,lcpTs:l,phases:f(o,p,g,m),shouldRemoveLazyLoading:"lazy"===T,shouldIncreasePriorityHint:"high"!==I,shouldPreloadImage:!h}}});const m=50;function p(t,e){const n=function(t){const e=new Map;for(const[n,r]of t)"network"===n.type&&e.set(n.request.requestId,{node:n,nodeTiming:r});return e}(e.metrics.firstContentfulPaint.optimisticEstimate.nodeTimings),r={FCP:0,LCP:0},i=new Map,o=new Set;for(const e of t){const t=n.get(e.args.data.requestId);if(!t)continue;const{node:r,nodeTiming:a}=t;r.traverse((t=>o.add(t.id)));const s=Math.round(a.duration);s<m||i.set(r.id,s)}return i.size&&(r.FCP=function(t,e){const n=e.simulator,r=e.metrics.firstContentfulPaint.optimisticGraph,{nodeTimings:i}=e.simulator.simulate(r),o=new Map(i),a=r.cloneWithRelationships((e=>!t.has(e.id)));if("network"!==a.type)throw new Error("minimalFCPGraph not a NetworkNode");const s=Math.max(...Array.from(Array.from(o).map((t=>t[1].endTime)))),c=a.request.transferSize,d=c||0;a.request.transferSize=d+0;const u=n.simulate(a).timeInMs;return a.request.transferSize=c,Math.round(Math.max(s-u,0))}(o,e)),{metricSavings:r,requestIdToWastedMs:i}}var T=Object.freeze({__proto__:null,deps:function(){return["NetworkRequests","PageLoadMetrics"]},generateInsight:function(t,n){const r=t.PageLoadMetrics.metricScoresByFrameId.get(n.frameId)?.get(n.navigationId)?.get("FP")?.event?.ts;if(!r)return{renderBlockingRequests:[],warnings:[d.NO_FP]};const i=[];for(const o of t.NetworkRequests.byTime){if(o.args.data.frame!==n.frameId)continue;if("blocking"!==o.args.data.renderBlocking&&"in_body_parser_blocking"!==o.args.data.renderBlocking)continue;if(o.args.data.syntheticData.finishTime>r)continue;if("in_body_parser_blocking"===o.args.data.renderBlocking){const t=o.args.data.priority,e="Script"===o.args.data.resourceType;if("VeryHigh"!==t&&!(e&&"High"===t))continue}const a=e.Trace.getNavigationForTraceEvent(o,n.frameId,t.Meta.navigationsByFrameId);a?.args.data?.navigationId===n.navigationId&&i.push(o)}return{renderBlockingRequests:i,...n.lantern&&p(i,n.lantern)}}});var h=Object.freeze({__proto__:null,deps:function(){return["Meta","UserInteractions"]},generateInsight:function(t,n){const r=t.UserInteractions.beginCommitCompositorFrameEvents.filter((r=>{if(r.args.frame!==n.frameId)return!1;const i=e.Trace.getNavigationForTraceEvent(r,n.frameId,t.Meta.navigationsByFrameId);return i?.args.data?.navigationId===n.navigationId}));if(!r.length)return{mobileOptimized:null,warnings:[d.NO_LAYOUT]};for(const t of r)if(!t.args.is_mobile_optimized)return{mobileOptimized:!1};return{mobileOptimized:!0}}}),I=Object.freeze({__proto__:null,CumulativeLayoutShift:s,DocumentLatency:c,InteractionToNextPaint:u,LargestContentfulPaint:l,RenderBlocking:T,Viewport:h});export{I as InsightRunners,g as Types};
