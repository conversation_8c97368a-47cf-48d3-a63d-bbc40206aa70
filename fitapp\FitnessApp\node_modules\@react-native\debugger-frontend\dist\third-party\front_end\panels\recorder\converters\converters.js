import*as e from"../../../third_party/puppeteer-replay/puppeteer-replay.js";import"../models/models.js";var t=Object.freeze({__proto__:null});const n="extension_";var r=Object.freeze({__proto__:null,EXTENSION_PREFIX:n,ExtensionConverter:class{#e;#t;constructor(e,t){this.#e=e,this.#t=t}getId(){return n+this.#e}getFormatName(){return this.#t.getName()}getMediaType(){return this.#t.getMediaType()}getFilename(e){const t=this.#n(this.#t.getMediaType());return`${e.title}${t}`}async stringify(t){const n=await this.#t.stringify(t),r=e.parseSourceMap(n);return[e.stripSourceMap(n),r]}async stringifyStep(e){return await this.#t.stringifyStep(e)}#n(e){switch(e){case"application/json":return".json";case"application/javascript":case"text/javascript":return".js";case"application/typescript":case"text/typescript":return".ts";default:return""}}}});var i=Object.freeze({__proto__:null,JSONConverter:class{#r;constructor(e){this.#r=e}getId(){return"json"}getFormatName(){return"JSON"}getFilename(e){return`${e.title}.json`}async stringify(t){const n=await e.stringify(t,{extension:new e.JSONStringifyExtension,indentation:this.#r}),r=e.parseSourceMap(n);return[e.stripSourceMap(n),r]}async stringifyStep(t){return await e.stringifyStep(t,{extension:new e.JSONStringifyExtension,indentation:this.#r})}getMediaType(){return"application/json"}}});var s=Object.freeze({__proto__:null,LighthouseConverter:class{#r;constructor(e){this.#r=e}getId(){return"lighthouse"}getFormatName(){return"Puppeteer (including Lighthouse analysis)"}getFilename(e){return`${e.title}.js`}async stringify(t){const n=await e.stringify(t,{extension:new e.LighthouseStringifyExtension,indentation:this.#r}),r=e.parseSourceMap(n);return[e.stripSourceMap(n),r]}async stringifyStep(t){return await e.stringifyStep(t,{indentation:this.#r})}getMediaType(){return"text/javascript"}}});var a=Object.freeze({__proto__:null,PuppeteerConverter:class{#r;constructor(e){this.#r=e}getId(){return"puppeteer"}getFormatName(){return"Puppeteer"}getFilename(e){return`${e.title}.js`}async stringify(t){const n=await e.stringify(t,{indentation:this.#r}),r=e.parseSourceMap(n);return[e.stripSourceMap(n),r]}async stringifyStep(t){return await e.stringifyStep(t,{indentation:this.#r})}getMediaType(){return"text/javascript"}}});var o=Object.freeze({__proto__:null,PuppeteerReplayConverter:class{#r;constructor(e){this.#r=e}getId(){return"@puppeteer/replay"}getFormatName(){return"@puppeteer/replay"}getFilename(e){return`${e.title}.js`}async stringify(t){const n=await e.stringify(t,{extension:new e.PuppeteerReplayStringifyExtension,indentation:this.#r}),r=e.parseSourceMap(n);return[e.stripSourceMap(n),r]}async stringifyStep(t){return await e.stringifyStep(t,{extension:new e.PuppeteerReplayStringifyExtension})}getMediaType(){return"text/javascript"}}});export{t as Converter,r as ExtensionConverter,i as JSONConverter,s as LighthouseConverter,a as PuppeteerConverter,o as PuppeteerReplayConverter};
