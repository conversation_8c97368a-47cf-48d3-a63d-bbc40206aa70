{"version": 3, "file": "withIosSplashScreen.js", "names": ["_configPlugins", "data", "require", "_debug", "_interopRequireDefault", "_getIosSplashConfig", "_withIosSplashAssets", "_withIosSplashColors", "_withIosSplashInfoPlist", "_withIosSplashScreenStoryboard", "_withIosSplashScreenStoryboardImage", "_withIosSplashXcodeProject", "e", "__esModule", "default", "debug", "Debug", "withIosSplashScreen", "config", "props", "splashConfig", "getIosSplashConfig", "with<PERSON><PERSON><PERSON>", "withIosSplashInfoPlist", "withIosSplashAssets", "withIosSplashColors", "withIosSplashScreenImage", "withIosSplashXcodeProject", "withIosSplashScreenStoryboardBaseMod", "exports"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withIosSplashScreen.ts"], "sourcesContent": ["import { ConfigPlugin, withPlugins } from '@expo/config-plugins';\nimport Debug from 'debug';\n\nimport { getIosSplashConfig, IOSSplashConfig } from './getIosSplashConfig';\nimport { withIosSplashAssets } from './withIosSplashAssets';\nimport { withIosSplashColors } from './withIosSplashColors';\nimport { withIosSplashInfoPlist } from './withIosSplashInfoPlist';\nimport { withIosSplashScreenStoryboardBaseMod } from './withIosSplashScreenStoryboard';\nimport { withIosSplashScreenImage } from './withIosSplashScreenStoryboardImage';\nimport { withIosSplashXcodeProject } from './withIosSplashXcodeProject';\n\nconst debug = Debug('expo:prebuild-config:expo-splash-screen:ios');\n\nexport const withIosSplashScreen: ConfigPlugin<IOSSplashConfig | undefined | null | void> = (\n  config,\n  props\n) => {\n  // If the user didn't specify a splash object, infer the splash object from the Expo config.\n  const splashConfig = getIosSplashConfig(config, props ?? null);\n\n  debug(`config:`, props);\n\n  return withPlugins(config, [\n    [withIosSplashInfoPlist, splashConfig],\n    [withIosSplashAssets, splashConfig],\n    [withIosSplashColors, splashConfig],\n    // Add the image settings to the storyboard.\n    [withIosSplashScreenImage, splashConfig],\n    // Link storyboard to xcode project.\n    // TODO: Maybe fold this into the base mod.\n    withIosSplashXcodeProject,\n    // Insert the base mod last, no other ios.splashScreenStoryboard mods can be added after this.\n    withIosSplashScreenStoryboardBaseMod,\n  ]);\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,OAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,oBAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,mBAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,qBAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,oBAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,qBAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,oBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,wBAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,uBAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,+BAAA;EAAA,MAAAR,IAAA,GAAAC,OAAA;EAAAO,8BAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,oCAAA;EAAA,MAAAT,IAAA,GAAAC,OAAA;EAAAQ,mCAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,2BAAA;EAAA,MAAAV,IAAA,GAAAC,OAAA;EAAAS,0BAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAwE,SAAAG,uBAAAQ,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAExE,MAAMG,KAAK,GAAG,IAAAC,gBAAK,EAAC,6CAA6C,CAAC;AAE3D,MAAMC,mBAA4E,GAAGA,CAC1FC,MAAM,EACNC,KAAK,KACF;EACH;EACA,MAAMC,YAAY,GAAG,IAAAC,wCAAkB,EAACH,MAAM,EAAEC,KAAK,IAAI,IAAI,CAAC;EAE9DJ,KAAK,CAAC,SAAS,EAAEI,KAAK,CAAC;EAEvB,OAAO,IAAAG,4BAAW,EAACJ,MAAM,EAAE,CACzB,CAACK,gDAAsB,EAAEH,YAAY,CAAC,EACtC,CAACI,0CAAmB,EAAEJ,YAAY,CAAC,EACnC,CAACK,0CAAmB,EAAEL,YAAY,CAAC;EACnC;EACA,CAACM,8DAAwB,EAAEN,YAAY,CAAC;EACxC;EACA;EACAO,sDAAyB;EACzB;EACAC,qEAAoC,CACrC,CAAC;AACJ,CAAC;AAACC,OAAA,CAAAZ,mBAAA,GAAAA,mBAAA", "ignoreList": []}