"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _classStaticPrivateFieldSpecGet;
var _classApplyDescriptorGet = require("classApplyDescriptorGet");
var _assertClassBrand = require("assertClassBrand");
var _classCheckPrivateStaticFieldDescriptor = require("classCheckPrivateStaticFieldDescriptor");
function _classStaticPrivateFieldSpecGet(receiver, classConstructor, descriptor) {
  _assertClassBrand(classConstructor, receiver);
  _classCheckPrivateStaticFieldDescriptor(descriptor, "get");
  return _classApplyDescriptorGet(receiver, descriptor);
}

//# sourceMappingURL=classStaticPrivateFieldSpecGet.js.map
