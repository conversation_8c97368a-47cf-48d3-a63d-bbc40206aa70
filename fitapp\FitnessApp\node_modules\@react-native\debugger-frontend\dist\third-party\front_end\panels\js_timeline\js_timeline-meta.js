import*as e from"../../core/i18n/i18n.js";import*as i from"../../ui/legacy/legacy.js";const t={performance:"Performance",showPerformance:"Show Performance",showRecentTimelineSessions:"Show recent timeline sessions",record:"Record",stop:"Stop",recordAndReload:"Record and reload"},n=e.i18n.registerUIStrings("panels/js_timeline/js_timeline-meta.ts",t),o=e.i18n.getLazilyComputedLocalizedString.bind(void 0,n);let a;async function r(){return a||(a=await import("../timeline/timeline.js")),a}function l(e){return void 0===a?[]:e(a)}i.ViewManager.registerViewExtension({location:"panel",id:"timeline",title:o(t.performance),commandPrompt:o(t.showPerformance),order:66,hasToolbar:!1,isPreviewFeature:!0,loadView:async()=>(await r()).TimelinePanel.TimelinePanel.instance({forceNew:null,isNode:!0})}),i.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>new((await r()).TimelinePanel.ActionDelegate),category:"PERFORMANCE",title:o(t.showRecentTimelineSessions),contextTypes:()=>l((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:"PERFORMANCE",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>l((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await r()).TimelinePanel.ActionDelegate),options:[{value:!0,title:o(t.record)},{value:!1,title:o(t.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>l((e=>[e.TimelinePanel.TimelinePanel])),category:"PERFORMANCE",title:o(t.recordAndReload),loadActionDelegate:async()=>new((await r()).TimelinePanel.ActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]});
