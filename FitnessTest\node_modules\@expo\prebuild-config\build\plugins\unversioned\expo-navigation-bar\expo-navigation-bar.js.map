{"version": 3, "file": "expo-navigation-bar.js", "names": ["_withAndroidNavigationBar", "data", "require", "_createLegacyPlugin", "_default", "exports", "default", "createLegacyPlugin", "packageName", "fallback", "withNavigationBar"], "sources": ["../../../../src/plugins/unversioned/expo-navigation-bar/expo-navigation-bar.ts"], "sourcesContent": ["import { withNavigationBar } from './withAndroidNavigationBar';\nimport { createLegacyPlugin } from '../createLegacyPlugin';\n\nexport default createLegacyPlugin({\n  packageName: 'expo-navigation-bar',\n  fallback: [\n    // Android\n    withNavigationBar,\n  ],\n});\n"], "mappings": ";;;;;;AAAA,SAAAA,0BAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,yBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,oBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,mBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2D,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE5C,IAAAC,wCAAkB,EAAC;EAChCC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE;EACR;EACAC,6CAAiB;AAErB,CAAC,CAAC", "ignoreList": []}