/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

import type TerminalReporter from "metro/src/lib/TerminalReporter";

type LoggerFn = (...message: $ReadOnlyArray<string>) => void;

/**
 * Create a dev-middleware logger object that will emit logs via Metro's
 * terminal reporter.
 */
declare export default function createDevMiddlewareLogger(
  reporter: TerminalReporter
): $ReadOnly<{
  info: LoggerFn,
  error: LoggerFn,
  warn: LoggerFn,
}>;
