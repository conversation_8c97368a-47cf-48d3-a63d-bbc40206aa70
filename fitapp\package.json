{"name": "fitapp", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/google": "^1.2.18", "@ai-sdk/openai": "^1.3.22", "@expo/vector-icons": "^14.1.0", "@mastra/core": "^0.10.1", "@mastra/libsql": "^0.10.0", "@mastra/memory": "^0.10.1", "@modelcontextprotocol/sdk": "^1.12.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "expo-linear-gradient": "~14.1.4", "react-native-gesture-handler": "^2.25.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.0", "react-native-vector-icons": "^10.2.0", "zod": "^3.25.32"}, "devDependencies": {"@types/node": "^22.15.23", "mastra": "^0.10.1", "typescript": "^5.8.3", "@types/react": "~19.0.10"}}