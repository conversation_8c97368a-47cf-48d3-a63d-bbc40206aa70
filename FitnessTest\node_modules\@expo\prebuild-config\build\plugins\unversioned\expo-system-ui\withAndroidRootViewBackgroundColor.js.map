{"version": 3, "file": "withAndroidRootViewBackgroundColor.js", "names": ["_configPlugins", "data", "require", "assignColorValue", "AndroidConfig", "Colors", "assignStylesValue", "getAppThemeGroup", "Styles", "ANDROID_WINDOW_<PERSON>C<PERSON>GROUND", "WINDOW_BACKGROUND_COLOR", "withAndroidRootViewBackgroundColor", "config", "withRootViewBackgroundColorColors", "withRootViewBackgroundColorStyles", "exports", "withAndroidColors", "modResults", "value", "getRootViewBackgroundColor", "name", "withAndroidStyles", "add", "parent", "android", "backgroundColor"], "sources": ["../../../../src/plugins/unversioned/expo-system-ui/withAndroidRootViewBackgroundColor.ts"], "sourcesContent": ["import {\n  AndroidConfig,\n  ConfigPlugin,\n  withAndroidColors,\n  withAndroidStyles,\n} from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\n\nconst { assignColorValue } = AndroidConfig.Colors;\nconst { assignStylesValue, getAppThemeGroup } = AndroidConfig.Styles;\n\nconst ANDROID_WINDOW_BACKGROUND = 'android:windowBackground';\nconst WINDOW_BACKGROUND_COLOR = 'activityBackground';\n\nexport const withAndroidRootViewBackgroundColor: ConfigPlugin = (config) => {\n  config = withRootViewBackgroundColorColors(config);\n  config = withRootViewBackgroundColorStyles(config);\n  return config;\n};\n\nexport const withRootViewBackgroundColorColors: ConfigPlugin = (config) => {\n  return withAndroidColors(config, async (config) => {\n    config.modResults = assignColorValue(config.modResults, {\n      value: getRootViewBackgroundColor(config),\n      name: WINDOW_BACKGROUND_COLOR,\n    });\n    return config;\n  });\n};\n\nexport const withRootViewBackgroundColorStyles: ConfigPlugin = (config) => {\n  return withAndroidStyles(config, async (config) => {\n    config.modResults = assignStylesValue(config.modResults, {\n      add: !!getRootViewBackgroundColor(config),\n      parent: getAppThemeGroup(),\n      name: ANDROID_WINDOW_BACKGROUND,\n      value: `@color/${WINDOW_BACKGROUND_COLOR}`,\n    });\n    return config;\n  });\n};\n\nexport function getRootViewBackgroundColor(\n  config: Pick<ExpoConfig, 'android' | 'backgroundColor'>\n) {\n  return config.android?.backgroundColor || config.backgroundColor || null;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA,MAAM;EAAEE;AAAiB,CAAC,GAAGC,8BAAa,CAACC,MAAM;AACjD,MAAM;EAAEC,iBAAiB;EAAEC;AAAiB,CAAC,GAAGH,8BAAa,CAACI,MAAM;AAEpE,MAAMC,yBAAyB,GAAG,0BAA0B;AAC5D,MAAMC,uBAAuB,GAAG,oBAAoB;AAE7C,MAAMC,kCAAgD,GAAIC,MAAM,IAAK;EAC1EA,MAAM,GAAGC,iCAAiC,CAACD,MAAM,CAAC;EAClDA,MAAM,GAAGE,iCAAiC,CAACF,MAAM,CAAC;EAClD,OAAOA,MAAM;AACf,CAAC;AAACG,OAAA,CAAAJ,kCAAA,GAAAA,kCAAA;AAEK,MAAME,iCAA+C,GAAID,MAAM,IAAK;EACzE,OAAO,IAAAI,kCAAiB,EAACJ,MAAM,EAAE,MAAOA,MAAM,IAAK;IACjDA,MAAM,CAACK,UAAU,GAAGd,gBAAgB,CAACS,MAAM,CAACK,UAAU,EAAE;MACtDC,KAAK,EAAEC,0BAA0B,CAACP,MAAM,CAAC;MACzCQ,IAAI,EAAEV;IACR,CAAC,CAAC;IACF,OAAOE,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACG,OAAA,CAAAF,iCAAA,GAAAA,iCAAA;AAEK,MAAMC,iCAA+C,GAAIF,MAAM,IAAK;EACzE,OAAO,IAAAS,kCAAiB,EAACT,MAAM,EAAE,MAAOA,MAAM,IAAK;IACjDA,MAAM,CAACK,UAAU,GAAGX,iBAAiB,CAACM,MAAM,CAACK,UAAU,EAAE;MACvDK,GAAG,EAAE,CAAC,CAACH,0BAA0B,CAACP,MAAM,CAAC;MACzCW,MAAM,EAAEhB,gBAAgB,CAAC,CAAC;MAC1Ba,IAAI,EAAEX,yBAAyB;MAC/BS,KAAK,EAAE,UAAUR,uBAAuB;IAC1C,CAAC,CAAC;IACF,OAAOE,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACG,OAAA,CAAAD,iCAAA,GAAAA,iCAAA;AAEK,SAASK,0BAA0BA,CACxCP,MAAuD,EACvD;EACA,OAAOA,MAAM,CAACY,OAAO,EAAEC,eAAe,IAAIb,MAAM,CAACa,eAAe,IAAI,IAAI;AAC1E", "ignoreList": []}