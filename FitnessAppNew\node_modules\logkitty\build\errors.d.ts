export declare class CodeError extends Error {
    code: string;
    constructor(code: string, message?: string);
}
export declare const ERR_ANDROID_UNPROCESSABLE_PID = "ERR_ANDROID_UNPROCESSABLE_PID";
export declare const ERR_ANDROID_CANNOT_GET_APP_PID = "ERR_ANDROID_CANNOT_GET_APP_PID";
export declare const ERR_ANDROID_CANNOT_CLEAN_LOGCAT_BUFFER = "ERR_ANDROID_CANNOT_CLEAN_LOGCAT_BUFFER";
export declare const ERR_ANDROID_CANNOT_START_LOGCAT = "ERR_ANDROID_CANNOT_START_LOGCAT";
export declare const ERR_IOS_CANNOT_LIST_SIMULATORS = "ERR_IOS_CANNOT_LIST_SIMULATORS";
export declare const ERR_IOS_NO_SIMULATORS_BOOTED = "ERR_IOS_NO_SIMULATORS_BOOTED";
export declare const ERR_IOS_CANNOT_START_SYSLOG = "ERR_IOS_CANNOT_START_SYSLOG";
//# sourceMappingURL=errors.d.ts.map