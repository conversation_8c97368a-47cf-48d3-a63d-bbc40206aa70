// Antrenman verileri ve programları

export const workoutDatabase = {
  // BMI kategorisine göre antrenman önerileri
  workoutsByBMI: {
    'zayıf': {
      focus: 'Kas kütlesi artırma ve güç kazanma',
      types: ['güç_antrenmanı', 'karma'],
      intensity: 'orta-yüksek',
      frequency: '4-5 gün/hafta'
    },
    'normal': {
      focus: 'Genel fitness ve sağlık',
      types: ['karma', 'kardiyovasküler', 'güç_antrenmanı'],
      intensity: 'orta',
      frequency: '4-6 gün/hafta'
    },
    'fazla_kilolu': {
      focus: 'Kilo verme ve kardiyovasküler sağlık',
      types: ['kardiyovasküler', 'karma'],
      intensity: 'orta',
      frequency: '5-6 gün/hafta'
    },
    'obez_1': {
      focus: 'Güvenli kilo verme',
      types: ['düşük_etkili_kardiyovasküler'],
      intensity: 'düşük-orta',
      frequency: '5-7 gün/hafta'
    },
    'obez_2': {
      focus: 'Çok güvenli aktivite',
      types: ['su_sporları', 'yürüyüş'],
      intensity: 'düşük',
      frequency: 'günlük'
    },
    'obez_3': {
      focus: 'Doktor kontrolünde aktivite',
      types: ['fizik_tedavi'],
      intensity: 'çok_düşük',
      frequency: 'doktor_önerisi'
    }
  },

  // Egzersiz programları
  exercises: {
    kardiyovasküler: {
      başlangıç: [
        {
          name: 'Yürüyüş',
          duration: '20-30 dakika',
          description: 'Orta tempoda yürüyüş, nabız 120-140 arası'
        },
        {
          name: 'Sabit Bisiklet',
          duration: '15-25 dakika',
          description: 'Düşük dirençte, rahat tempo'
        },
        {
          name: 'Yüzme',
          duration: '20-30 dakika',
          description: 'Serbest stil, molalarla'
        }
      ],
      orta: [
        {
          name: 'Koşu',
          duration: '25-40 dakika',
          description: 'Orta tempo koşu, nabız 140-160 arası'
        },
        {
          name: 'Bisiklet',
          duration: '30-45 dakika',
          description: 'Orta direnç, değişken tempo'
        },
        {
          name: 'Eliptik',
          duration: '25-35 dakika',
          description: 'Orta-yüksek yoğunluk'
        }
      ],
      ileri: [
        {
          name: 'HIIT Koşu',
          duration: '20-30 dakika',
          description: 'Yüksek yoğunluk interval antrenmanı'
        },
        {
          name: 'Sprint',
          duration: '15-25 dakika',
          description: 'Sprint + dinlenme döngüleri'
        },
        {
          name: 'Crossfit Cardio',
          duration: '30-45 dakika',
          description: 'Karma kardiyovasküler egzersizler'
        }
      ]
    },

    güç_antrenmanı: {
      başlangıç: [
        {
          name: 'Squat',
          sets: 3,
          reps: '8-12',
          description: 'Vücut ağırlığı ile, doğru form'
        },
        {
          name: 'Push-up',
          sets: 3,
          reps: '5-10',
          description: 'Dizler üzerinde başlayabilirsiniz'
        },
        {
          name: 'Plank',
          sets: 3,
          duration: '20-30 saniye',
          description: 'Karın kasları için temel egzersiz'
        },
        {
          name: 'Lunges',
          sets: 3,
          reps: '8-10 her bacak',
          description: 'Vücut ağırlığı ile'
        }
      ],
      orta: [
        {
          name: 'Dumbbell Squat',
          sets: 4,
          reps: '10-15',
          description: 'Hafif ağırlıklarla'
        },
        {
          name: 'Bench Press',
          sets: 4,
          reps: '8-12',
          description: 'Orta ağırlıkta'
        },
        {
          name: 'Deadlift',
          sets: 3,
          reps: '6-10',
          description: 'Doğru teknikle, orta ağırlık'
        },
        {
          name: 'Pull-ups',
          sets: 3,
          reps: '5-8',
          description: 'Yardımcı bantla başlayabilirsiniz'
        }
      ],
      ileri: [
        {
          name: 'Barbell Squat',
          sets: 5,
          reps: '5-8',
          description: 'Ağır ağırlıklarla'
        },
        {
          name: 'Deadlift',
          sets: 5,
          reps: '3-6',
          description: 'Ağır ağırlık, mükemmel form'
        },
        {
          name: 'Overhead Press',
          sets: 4,
          reps: '6-10',
          description: 'Omuz ve core gücü'
        },
        {
          name: 'Weighted Pull-ups',
          sets: 4,
          reps: '5-8',
          description: 'Ek ağırlıkla'
        }
      ]
    },

    yoga: {
      başlangıç: [
        {
          name: 'Güneş Selamı A',
          duration: '10-15 dakika',
          description: 'Temel yoga akışı'
        },
        {
          name: 'Nefes Egzersizleri',
          duration: '5-10 dakika',
          description: 'Pranayama teknikleri'
        },
        {
          name: 'Temel Pozlar',
          duration: '15-20 dakika',
          description: 'Warrior, Tree, Child pose'
        }
      ],
      orta: [
        {
          name: 'Vinyasa Flow',
          duration: '30-45 dakika',
          description: 'Akışkan hareket dizisi'
        },
        {
          name: 'Güneş Selamı B',
          duration: '15-20 dakika',
          description: 'Daha karmaşık akış'
        },
        {
          name: 'Denge Pozları',
          duration: '10-15 dakika',
          description: 'Crow, Eagle, Dancer pose'
        }
      ],
      ileri: [
        {
          name: 'Ashtanga Yoga',
          duration: '60-90 dakika',
          description: 'Geleneksel seri'
        },
        {
          name: 'İleri Pozlar',
          duration: '20-30 dakika',
          description: 'Handstand, Scorpion, King Pigeon'
        },
        {
          name: 'Meditasyon',
          duration: '15-30 dakika',
          description: 'Derin nefes ve farkındalık'
        }
      ]
    }
  },

  // Haftalık program şablonları
  weeklyPrograms: {
    kilo_verme: {
      pazartesi: { type: 'kardiyovasküler', duration: 45 },
      salı: { type: 'güç_antrenmanı', duration: 40 },
      çarşamba: { type: 'kardiyovasküler', duration: 30 },
      perşembe: { type: 'güç_antrenmanı', duration: 40 },
      cuma: { type: 'kardiyovasküler', duration: 45 },
      cumartesi: { type: 'yoga', duration: 30 },
      pazar: { type: 'dinlenme', duration: 0 }
    },
    kas_kazanma: {
      pazartesi: { type: 'güç_antrenmanı', duration: 60 },
      salı: { type: 'kardiyovasküler', duration: 20 },
      çarşamba: { type: 'güç_antrenmanı', duration: 60 },
      perşembe: { type: 'dinlenme', duration: 0 },
      cuma: { type: 'güç_antrenmanı', duration: 60 },
      cumartesi: { type: 'kardiyovasküler', duration: 30 },
      pazar: { type: 'yoga', duration: 45 }
    },
    genel_sağlık: {
      pazartesi: { type: 'kardiyovasküler', duration: 30 },
      salı: { type: 'güç_antrenmanı', duration: 45 },
      çarşamba: { type: 'yoga', duration: 30 },
      perşembe: { type: 'kardiyovasküler', duration: 30 },
      cuma: { type: 'güç_antrenmanı', duration: 45 },
      cumartesi: { type: 'karma', duration: 40 },
      pazar: { type: 'dinlenme', duration: 0 }
    }
  },

  // Güvenlik kuralları
  safetyGuidelines: [
    'Egzersize başlamadan önce 5-10 dakika ısınma yapın',
    'Egzersiz sonrası 5-10 dakika soğuma ve germe yapın',
    'Bol su için, dehidrasyon belirtilerine dikkat edin',
    'Ağrı hissederseniz egzersizi durdurun',
    'Doğru form ve tekniği öğrenin',
    'Kademeli olarak yoğunluğu artırın',
    'Dinlenme günlerini ihmal etmeyin',
    'Sağlık probleminiz varsa doktora danışın'
  ]
};
