import { z } from 'zod';

// BMI sonuç şeması
export const BMIResultSchema = z.object({
  bmi: z.number(),
  category: z.enum(['zayıf', 'normal', 'fazla_kilolu', 'obez_1', 'obez_2', 'obez_3']),
  categoryDescription: z.string(),
  healthRisk: z.string(),
  recommendations: z.array(z.string()),
  idealWeightRange: z.object({
    min: z.number(),
    max: z.number()
  })
});

// Beslenme önerisi şeması
export const NutritionPlanSchema = z.object({
  dailyCalorieNeeds: z.number(),
  macronutrients: z.object({
    protein: z.object({
      grams: z.number(),
      percentage: z.number()
    }),
    carbohydrates: z.object({
      grams: z.number(),
      percentage: z.number()
    }),
    fats: z.object({
      grams: z.number(),
      percentage: z.number()
    })
  }),
  mealPlan: z.object({
    breakfast: z.array(z.string()),
    lunch: z.array(z.string()),
    dinner: z.array(z.string()),
    snacks: z.array(z.string())
  }),
  nutritionTips: z.array(z.string()),
  foodsToAvoid: z.array(z.string()),
  hydrationGoal: z.string()
});

// Antrenman planı şeması
export const WorkoutPlanSchema = z.object({
  weeklySchedule: z.array(z.object({
    day: z.string(),
    workoutType: z.string(),
    duration: z.number(),
    exercises: z.array(z.object({
      name: z.string(),
      sets: z.number().optional(),
      reps: z.string().optional(),
      duration: z.string().optional(),
      description: z.string()
    }))
  })),
  progressionTips: z.array(z.string()),
  safetyGuidelines: z.array(z.string()),
  equipmentNeeded: z.array(z.string())
});

// Genel fitness değerlendirme şeması
export const FitnessAssessmentSchema = z.object({
  bmiResult: BMIResultSchema,
  nutritionPlan: NutritionPlanSchema,
  workoutPlan: WorkoutPlanSchema,
  overallRecommendations: z.array(z.string()),
  nextSteps: z.array(z.string())
});

export type BMIResult = z.infer<typeof BMIResultSchema>;
export type NutritionPlan = z.infer<typeof NutritionPlanSchema>;
export type WorkoutPlan = z.infer<typeof WorkoutPlanSchema>;
export type FitnessAssessment = z.infer<typeof FitnessAssessmentSchema>;
