import { createTool } from '@mastra/core';
import { BMIInputSchema } from '../schemas/user-profile.js';
import { calculateBMI } from '../utils/fitness-calculations.js';

export const bmiCalculatorTool = createTool({
  id: 'bmi-calculator',
  description: '<PERSON><PERSON>ıcının boy ve kilosuna göre BMI (Beden Kitle İndeksi) hesaplar ve sağlık durumu hakkında bilgi verir',
  inputSchema: BMIInputSchema,
  execute: async ({ context }) => {
    const { height, weight } = context;
    
    try {
      const bmiResult = calculateBMI(height, weight);
      
      return {
        success: true,
        data: bmiResult,
        message: `BMI hesaplaması tamamlandı. BMI değeriniz: ${bmiResult.bmi} (${bmiResult.categoryDescription})`
      };
    } catch (error) {
      return {
        success: false,
        error: 'BMI hesaplanırken bir hata oluştu',
        details: error instanceof Error ? error.message : '<PERSON><PERSON><PERSON>meyen hata'
      };
    }
  }
});
