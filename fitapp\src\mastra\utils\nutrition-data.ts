// Beslenme verileri ve önerileri

export const nutritionDatabase = {
  // BMI kategorisine göre beslenme önerileri
  nutritionByBMI: {
    'zayıf': {
      foods: [
        'Tam tahıllı ekmek ve makarna',
        '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (badem, cev<PERSON>, fınd<PERSON><PERSON>)',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>yağı',
        'Ya<PERSON><PERSON><PERSON> balıklar (somon, uskumru)',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>t ürünleri (peynir, yoğurt)',
        '<PERSON><PERSON> meyveler',
        'Protein tozu',
        'Kinoa'
      ],
      tips: [
        'Günde 5-6 küçük öğün tüketin',
        'Her öğünde protein bulundurun',
        'Sağlıklı yağları ihmal etmeyin',
        'Bol su için',
        'Vitamin ve mineral desteği alın'
      ],
      avoid: [
        'Aşır<PERSON> işlenmiş gıdalar',
        '<PERSON><PERSON><PERSON><PERSON> içecekler',
        '<PERSON> yağlar',
        '<PERSON><PERSON><PERSON><PERSON><PERSON> kafein'
      ]
    },
    'normal': {
      foods: [
        '<PERSON><PERSON><PERSON><PERSON> (brokoli, ıspanak, havuç)',
        '<PERSON><PERSON><PERSON><PERSON> (elma, muz, portakal)',
        'Yağsız protein (tavuk, balık, baklagiller)',
        'Tam tahıllar',
        'Düşük yağlı süt ürünleri',
        'Sağlıklı yağlar (zeytinyağı, avokado)',
        'Kuruyemişler (ölçülü)',
        '<PERSON>',
        'Yeşil çay'
      ],
      tips: [
        'Dengeli beslenmeye devam edin',
        'Porsiyon kontrolü yapın',
        'Günde en az 5 porsiyon sebze-meyve',
        'Düzenli öğün saatleri',
        'Bol su için'
      ],
      avoid: [
        'Aşırı şeker',
        'İşlenmiş gıdalar',
        'Aşırı tuz',
        'Alkol (sınırlı)'
      ]
    },
    'fazla_kilolu': {
      foods: [
        'Yapraklı yeşil sebzeler',
        'Düşük kalorili meyveler (çilek, böğürtlen)',
        'Yağsız protein (tavuk göğsü, balık)',
        'Baklagiller',
        'Tam tahıllar (sınırlı)',
        'Düşük yağlı süt ürünleri',
        'Su',
        'Yeşil çay',
        'Baharatlar (zerdeçal, tarçın)'
      ],
      tips: [
        'Kalori açığı oluşturun',
        'Porsiyon boyutlarını küçültün',
        'Yemek öncesi su için',
        'Yavaş yiyin',
        'Atıştırmalıkları kontrol edin'
      ],
      avoid: [
        'Şekerli içecekler',
        'Fast food',
        'İşlenmiş atıştırmalıklar',
        'Beyaz un ürünleri',
        'Aşırı yağlı yemekler'
      ]
    },
    'obez_1': {
      foods: [
        'Düşük kalorili sebzeler',
        'Protein (tavuk, balık, tofu)',
        'Baklagiller',
        'Düşük şekerli meyveler',
        'Tam tahıllar (çok sınırlı)',
        'Düşük yağlı süt ürünleri',
        'Su',
        'Bitki çayları'
      ],
      tips: [
        'Doktor kontrolünde beslenin',
        'Kalori sayımı yapın',
        'Öğün planlaması',
        'Destek grubu katılımı',
        'Düzenli tartılma'
      ],
      avoid: [
        'Şeker',
        'İşlenmiş gıdalar',
        'Alkol',
        'Yüksek kalorili içecekler',
        'Kızartmalar'
      ]
    },
    'obez_2': {
      foods: [
        'Çok düşük kalorili sebzeler',
        'Yağsız protein',
        'Sınırlı meyve',
        'Su',
        'Protein shakeları (doktor önerisi)'
      ],
      tips: [
        'Medikal beslenme programı',
        'Sıkı kalori kontrolü',
        'Uzman takibi',
        'Psikolojik destek',
        'Aile desteği'
      ],
      avoid: [
        'Tüm işlenmiş gıdalar',
        'Şeker ve tatlandırıcılar',
        'Yüksek kalorili her şey',
        'Alkol',
        'Karbonhidratlar (sınırlı)'
      ]
    },
    'obez_3': {
      foods: [
        'Doktor önerisi protein',
        'Çok sınırlı sebze',
        'Su',
        'Vitamin takviyeleri'
      ],
      tips: [
        'Sadece doktor kontrolünde',
        'Hastane takibi',
        'Özel beslenme programı',
        'Psikolojik destek',
        'Aile katılımı'
      ],
      avoid: [
        'Doktor izni olmayan her şey'
      ]
    }
  },

  // Öğün önerileri
  mealSuggestions: {
    breakfast: {
      'kilo_verme': [
        'Yumurta beyazı omlet + sebze',
        'Yoğurt + çilek',
        'Yulaf ezmesi + tarçın',
        'Protein smoothie'
      ],
      'kilo_alma': [
        'Tam yumurta + avokado + ekmek',
        'Yoğurt + granola + meyve',
        'Protein pancake',
        'Fındık ezmeli ekmek'
      ],
      'normal': [
        'Yumurta + sebze + ekmek',
        'Yoğurt + meyve + kuruyemiş',
        'Yulaf ezmesi + muz',
        'Peynir + domates + ekmek'
      ]
    },
    lunch: {
      'kilo_verme': [
        'Izgara tavuk + salata',
        'Balık + buharda sebze',
        'Mercimek çorbası',
        'Ton balığı salatası'
      ],
      'kilo_alma': [
        'Tavuk + pilav + sebze',
        'Somon + patates + salata',
        'Kıymalı makarna',
        'Etli nohut'
      ],
      'normal': [
        'Tavuk + bulgur + sebze',
        'Balık + salata',
        'Sebzeli makarna',
        'Mercimek yemeği'
      ]
    },
    dinner: {
      'kilo_verme': [
        'Izgara balık + salata',
        'Tavuk çorbası',
        'Sebze yemeği',
        'Protein + yeşillik'
      ],
      'kilo_alma': [
        'Et yemeği + pilav',
        'Tavuk + patates',
        'Balık + makarna',
        'Kıymalı sebze'
      ],
      'normal': [
        'Balık + sebze',
        'Tavuk + salata',
        'Sebzeli et',
        'Omlet + salata'
      ]
    },
    snacks: {
      'kilo_verme': [
        'Çiğ badem (10 adet)',
        'Yoğurt (1 kase)',
        'Elma (1 adet)',
        'Protein bar'
      ],
      'kilo_alma': [
        'Kuruyemiş karışımı',
        'Protein smoothie',
        'Avokado toast',
        'Granola bar'
      ],
      'normal': [
        'Meyve',
        'Yoğurt',
        'Kuruyemiş (ölçülü)',
        'Sebze çubukları'
      ]
    }
  }
};
