{"version": 3, "sources": ["../src/cli.ts"], "names": ["androidPriorityOptions", "unknown", "alias", "boolean", "default", "describe", "verbose", "debug", "info", "warn", "error", "fatal", "silent", "iosPriorityOptions", "argv", "_", "platform", "filter", "args", "yargs", "usage", "command", "demandCommand", "option", "type", "nargs", "example", "help", "version", "selectedAndroidPriorities", "Boolean", "selectedIosPriorities", "createFilter", "appId", "tags", "regexes", "map", "value", "RegExp", "patterns", "emitter", "adbPath", "String", "priority", "AndroidPriority", "DEBUG", "IosPriority", "DEFAULT", "on", "entry", "process", "stdout", "write", "terminate", "console", "log", "exit"], "mappings": ";;AAAA;;AACA;;AAUA;;AAEA;;;;AAGA,MAAMA,sBAAsB,GAAG;AAC7BC,EAAAA,OAAO,EAAE;AACPC,IAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,CADA;AAEPC,IAAAA,OAAO,EAAE,IAFF;AAGPC,IAAAA,OAAO,EAAE,KAHF;AAIPC,IAAAA,QAAQ,EAAE;AAJH,GADoB;AAO7BC,EAAAA,OAAO,EAAE;AACPJ,IAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,CADA;AAEPC,IAAAA,OAAO,EAAE,IAFF;AAGPC,IAAAA,OAAO,EAAE,KAHF;AAIPC,IAAAA,QAAQ,EAAE;AAJH,GAPoB;AAa7BE,EAAAA,KAAK,EAAE;AACLL,IAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,CADF;AAELC,IAAAA,OAAO,EAAE,IAFJ;AAGLC,IAAAA,OAAO,EAAE,KAHJ;AAILC,IAAAA,QAAQ,EAAE;AAJL,GAbsB;AAmB7BG,EAAAA,IAAI,EAAE;AACJN,IAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,CADH;AAEJC,IAAAA,OAAO,EAAE,IAFL;AAGJC,IAAAA,OAAO,EAAE,KAHL;AAIJC,IAAAA,QAAQ,EAAE;AAJN,GAnBuB;AAyB7BI,EAAAA,IAAI,EAAE;AACJP,IAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,CADH;AAEJC,IAAAA,OAAO,EAAE,IAFL;AAGJC,IAAAA,OAAO,EAAE,KAHL;AAIJC,IAAAA,QAAQ,EAAE;AAJN,GAzBuB;AA+B7BK,EAAAA,KAAK,EAAE;AACLR,IAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,CADF;AAELC,IAAAA,OAAO,EAAE,IAFJ;AAGLC,IAAAA,OAAO,EAAE,KAHJ;AAILC,IAAAA,QAAQ,EAAE;AAJL,GA/BsB;AAqC7BM,EAAAA,KAAK,EAAE;AACLT,IAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,CADF;AAELC,IAAAA,OAAO,EAAE,IAFJ;AAGLC,IAAAA,OAAO,EAAE,KAHJ;AAILC,IAAAA,QAAQ,EAAE;AAJL,GArCsB;AA2C7BO,EAAAA,MAAM,EAAE;AACNV,IAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,CADD;AAENC,IAAAA,OAAO,EAAE,IAFH;AAGNC,IAAAA,OAAO,EAAE,KAHH;AAINC,IAAAA,QAAQ,EAAE;AAJJ;AA3CqB,CAA/B;AAmDA,MAAMQ,kBAAkB,GAAG;AACzBN,EAAAA,KAAK,EAAE;AACLL,IAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,CADF;AAELC,IAAAA,OAAO,EAAE,IAFJ;AAGLC,IAAAA,OAAO,EAAE,KAHJ;AAILC,IAAAA,QAAQ,EAAE;AAJL,GADkB;AAOzBG,EAAAA,IAAI,EAAE;AACJN,IAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,CADH;AAEJC,IAAAA,OAAO,EAAE,IAFL;AAGJC,IAAAA,OAAO,EAAE,KAHL;AAIJC,IAAAA,QAAQ,EAAE;AAJN,GAPmB;AAazBK,EAAAA,KAAK,EAAE;AACLR,IAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,CADF;AAELC,IAAAA,OAAO,EAAE,IAFJ;AAGLC,IAAAA,OAAO,EAAE,KAHJ;AAILC,IAAAA,QAAQ,EAAE;AAJL;AAbkB,CAA3B;;AAqBA,MAAM;AACJS,EAAAA,IAAI,EAAE;AACJC,IAAAA,CAAC,EAAE,CAACC,QAAD,EAAWC,MAAX,CADC;AAEJ,OAAGC;AAFC;AADF,IAKFC,eACDC,KADC,CACK,gCADL,EAEDC,OAFC,CAEO,SAFP,EAEkB,SAFlB,EAE6BF,KAAK,IAClCA,KAAK,CACFE,OADH,CAEI,gBAFJ,EAGI,+BAHJ,EAIIrB,sBAJJ,EAMGqB,OANH,CAOI,aAPJ,EAQI,kDARJ,EASIrB,sBATJ,EAWGqB,OAXH,CAYI,oBAZJ,EAaI,mCAbJ,EAcIrB,sBAdJ,EAgBGqB,OAhBH,CAiBI,uBAjBJ,EAkBI,+CAlBJ,EAoBGA,OApBH,CAoBW,KApBX,EAoBkB,eApBlB,EAoBmCrB,sBApBnC,EAqBGsB,aArBH,CAqBiB,CArBjB,EAsBGC,MAtBH,CAsBU,UAtBV,EAsBsB;AAClBC,EAAAA,IAAI,EAAE,QADY;AAElBnB,EAAAA,QAAQ,EAAE,wBAFQ;AAGlBoB,EAAAA,KAAK,EAAE;AAHW,CAtBtB,EA2BGC,OA3BH,CA4BI,sBA5BJ,EA6BI,iDA7BJ,EA+BGA,OA/BH,CAgCI,yBAhCJ,EAiCI,6EAjCJ,EAmCGA,OAnCH,CAoCI,kCApCJ,EAqCI,sCArCJ,EAuCGA,OAvCH,CAwCI,yBAxCJ,EAyCI,yCAzCJ,EA2CGA,OA3CH,CA4CI,qCA5CJ,EA6CI,oEA7CJ,EA+CGA,OA/CH,CAgDI,+BAhDJ,EAiDI,8EAjDJ,CAHA,EAuDDL,OAvDC,CAuDO,cAvDP,EAuDuB,KAvDvB,EAuD8BF,KAAK,IACnCA,KAAK,CACFE,OADH,CAEI,gBAFJ,EAGI,+BAHJ,EAIIR,kBAJJ,EAMGQ,OANH,CAOI,oBAPJ,EAQI,mCARJ,EASIR,kBATJ,EAWGQ,OAXH,CAWW,KAXX,EAWkB,eAXlB,EAWmCR,kBAXnC,EAYGS,aAZH,CAYiB,CAZjB,EAaGI,OAbH,CAcI,kBAdJ,EAeI,iDAfJ,EAiBGA,OAjBH,CAkBI,qBAlBJ,EAmBI,6EAnBJ,EAqBGA,OArBH,CAqBW,qBArBX,EAqBkC,yCArBlC,CAxDA,EA+EDJ,aA/EC,CA+Ea,CA/Eb,EAgFDK,IAhFC,CAgFI,GAhFJ,EAiFDzB,KAjFC,CAiFK,GAjFL,EAiFU,MAjFV,EAkFDA,KAlFC,CAkFK,GAlFL,EAkFU,SAlFV,EAmFD0B,OAnFC,EALJ;;AA0FA,MAAMC,yBAAyB,GAAG;AAChC5B,EAAAA,OAAO,EAAE6B,OAAO,CAACZ,IAAI,CAACjB,OAAN,CADgB;AAEhCK,EAAAA,OAAO,EAAEwB,OAAO,CAACZ,IAAI,CAACZ,OAAN,CAFgB;AAGhCC,EAAAA,KAAK,EAAEuB,OAAO,CAACZ,IAAI,CAACX,KAAN,CAHkB;AAIhCC,EAAAA,IAAI,EAAEsB,OAAO,CAACZ,IAAI,CAACV,IAAN,CAJmB;AAKhCC,EAAAA,IAAI,EAAEqB,OAAO,CAACZ,IAAI,CAACT,IAAN,CALmB;AAMhCC,EAAAA,KAAK,EAAEoB,OAAO,CAACZ,IAAI,CAACR,KAAN,CANkB;AAOhCC,EAAAA,KAAK,EAAEmB,OAAO,CAACZ,IAAI,CAACP,KAAN,CAPkB;AAQhCC,EAAAA,MAAM,EAAEkB,OAAO,CAACZ,IAAI,CAACN,MAAN;AARiB,CAAlC;AAWA,MAAMmB,qBAAqB,GAAG;AAC5BxB,EAAAA,KAAK,EAAEuB,OAAO,CAACZ,IAAI,CAACX,KAAN,CADc;AAE5BC,EAAAA,IAAI,EAAEsB,OAAO,CAACZ,IAAI,CAACV,IAAN,CAFe;AAG5BE,EAAAA,KAAK,EAAEoB,OAAO,CAACZ,IAAI,CAACR,KAAN;AAHc,CAA9B;;AAMA,IAAI;AACF,MAAIsB,YAAJ;;AACA,UAAQf,MAAR;AACE,SAAK,KAAL;AACEe,MAAAA,YAAY,GAAG,wBAAcd,IAAI,CAACe,KAAnB,CAAf;AACA;;AACF,SAAK,KAAL;AACED,MAAAA,YAAY,GAAG,yBAAe,GAAId,IAAI,CAACgB,IAAxB,CAAf;AACA;;AACF,SAAK,OAAL;AACEF,MAAAA,YAAY,GAAG,0BACb,GAAId,IAAI,CAACiB,OAAN,CAA2BC,GAA3B,CACAC,KAAD,IAAmB,IAAIC,MAAJ,CAAWD,KAAX,EAAkB,IAAlB,CADlB,CADU,CAAf;AAKA;;AACF,SAAK,QAAL;AACEL,MAAAA,YAAY,GAAG,2BAAiB,GAAId,IAAI,CAACqB,QAA1B,CAAf;AACA;;AACF,SAAK,KAAL;AACA;AAlBF;;AAoBA,QAAMC,OAAO,GAAG,mBAAS;AACvBxB,IAAAA,QAAQ,EAAEA,QADa;AAEvByB,IAAAA,OAAO,EAAEvB,IAAI,CAACuB,OAAL,GAAeC,MAAM,CAACxB,IAAI,CAACuB,OAAN,CAArB,GAAsC,EAFxB;AAGvBE,IAAAA,QAAQ,EACN3B,QAAQ,KAAK,SAAb,GACI,2BACE4B,oBADF,EAEEf,yBAFF,EAGEe,qBAAgBC,KAHlB,CADJ,GAMI,2BACEC,gBADF,EAEEf,qBAFF,EAGEe,iBAAYC,OAHd,CAViB;AAevB9B,IAAAA,MAAM,EAAEe;AAfe,GAAT,CAAhB;AAkBAQ,EAAAA,OAAO,CAACQ,EAAR,CAAW,OAAX,EAAqBC,KAAD,IAAkB;AACpCC,IAAAA,OAAO,CAACC,MAAR,CAAeC,KAAf,CAAqB,6BAAYH,KAAZ,CAArB;AACD,GAFD;AAIAT,EAAAA,OAAO,CAACQ,EAAR,CAAW,OAAX,EAAqBtC,KAAD,IAAkB;AACpC2C,IAAAA,SAAS,CAAC3C,KAAD,CAAT;AACD,GAFD;AAGD,CA/CD,CA+CE,OAAOA,KAAP,EAAc;AACd2C,EAAAA,SAAS,CAAC3C,KAAD,CAAT;AACD;;AAED,SAAS2C,SAAT,CAAmB3C,KAAnB,EAA6C;AAC3C;AACA4C,EAAAA,OAAO,CAACC,GAAR,CAAY,6BAAY7C,KAAZ,CAAZ;AACAwC,EAAAA,OAAO,CAACM,IAAR,CAAa,CAAb;AACD", "sourcesContent": ["import yargs from 'yargs';\nimport {\n  logkitty,\n  makeAppFilter,\n  makeTagsFilter,\n  makeMatchFilter,\n  makeCustomFilter,\n  FilterCreator,\n  AndroidPriority,\n  IosPriority,\n} from './api';\nimport { formatEntry, formatError } from './formatters';\nimport { CodeError } from './errors';\nimport { getMinPriority } from './utils';\nimport { Entry, Platform } from './types';\n\nconst androidPriorityOptions = {\n  unknown: {\n    alias: ['U', 'u'],\n    boolean: true,\n    default: false,\n    describe: 'Unknown priority',\n  },\n  verbose: {\n    alias: ['V', 'v'],\n    boolean: true,\n    default: false,\n    describe: 'Verbose priority',\n  },\n  debug: {\n    alias: ['D', 'd'],\n    boolean: true,\n    default: false,\n    describe: 'Debug priority',\n  },\n  info: {\n    alias: ['I', 'i'],\n    boolean: true,\n    default: false,\n    describe: 'Info priority',\n  },\n  warn: {\n    alias: ['W', 'w'],\n    boolean: true,\n    default: false,\n    describe: 'Warn priority',\n  },\n  error: {\n    alias: ['E', 'e'],\n    boolean: true,\n    default: false,\n    describe: 'Error priority',\n  },\n  fatal: {\n    alias: ['F', 'f'],\n    boolean: true,\n    default: false,\n    describe: 'Fatal priority',\n  },\n  silent: {\n    alias: ['S', 's'],\n    boolean: true,\n    default: false,\n    describe: 'Silent priority',\n  },\n};\n\nconst iosPriorityOptions = {\n  debug: {\n    alias: ['D', 'd'],\n    boolean: true,\n    default: false,\n    describe: 'Debug level',\n  },\n  info: {\n    alias: ['I', 'i'],\n    boolean: true,\n    default: false,\n    describe: 'Info level',\n  },\n  error: {\n    alias: ['E', 'e'],\n    boolean: true,\n    default: false,\n    describe: 'Error level',\n  },\n};\n\nconst {\n  argv: {\n    _: [platform, filter],\n    ...args\n  },\n} = yargs\n  .usage('Usage: $0 [options] <platform>')\n  .command('android', 'Android', yargs =>\n    yargs\n      .command(\n        'tag <tags ...>',\n        'Show logs matching given tags',\n        androidPriorityOptions\n      )\n      .command(\n        'app <appId>',\n        'Show logs from application with given identifier',\n        androidPriorityOptions\n      )\n      .command(\n        'match <regexes...>',\n        'Show logs matching given patterns',\n        androidPriorityOptions\n      )\n      .command(\n        'custom <patterns ...>',\n        'Filter using custom patterns <tag>:<priority>'\n      )\n      .command('all', 'Show all logs', androidPriorityOptions)\n      .demandCommand(1)\n      .option('adb-path', {\n        type: 'string',\n        describe: 'Use custom path to adb',\n        nargs: 1,\n      })\n      .example(\n        '$0 android tag MyTag',\n        'Filter logs to only include ones with MyTag tag'\n      )\n      .example(\n        '$0 android tag MyTag -I',\n        'Filter logs to only include ones with MyTag tag and priority INFO and above'\n      )\n      .example(\n        '$0 android app com.example.myApp',\n        'Show all logs from com.example.myApp'\n      )\n      .example(\n        '$0 android match device',\n        'Show all logs matching /device/gm regex'\n      )\n      .example(\n        '$0 android app com.example.myApp -E',\n        'Show all logs from com.example.myApp with priority ERROR and above'\n      )\n      .example(\n        '$0 android custom *:S MyTag:D',\n        'Silence all logs and show only ones with MyTag with priority DEBUG and above'\n      )\n  )\n  .command('ios <filter>', 'iOS', yargs =>\n    yargs\n      .command(\n        'tag <tags ...>',\n        'Show logs matching given tags',\n        iosPriorityOptions\n      )\n      .command(\n        'match <regexes...>',\n        'Show logs matching given patterns',\n        iosPriorityOptions\n      )\n      .command('all', 'Show all logs', iosPriorityOptions)\n      .demandCommand(1)\n      .example(\n        '$0 ios tag MyTag',\n        'Filter logs to only include ones with MyTag tag'\n      )\n      .example(\n        '$0 ios tag MyTag -i',\n        'Filter logs to only include ones with MyTag tag and priority Info and Error'\n      )\n      .example('$0 ios match device', 'Show all logs matching /device/gm regex')\n  )\n  .demandCommand(1)\n  .help('h')\n  .alias('h', 'help')\n  .alias('v', 'version')\n  .version();\n\nconst selectedAndroidPriorities = {\n  unknown: Boolean(args.unknown),\n  verbose: Boolean(args.verbose),\n  debug: Boolean(args.debug),\n  info: Boolean(args.info),\n  warn: Boolean(args.warn),\n  error: Boolean(args.error),\n  fatal: Boolean(args.fatal),\n  silent: Boolean(args.silent),\n};\n\nconst selectedIosPriorities = {\n  debug: Boolean(args.debug),\n  info: Boolean(args.info),\n  error: Boolean(args.error),\n};\n\ntry {\n  let createFilter: FilterCreator | undefined;\n  switch (filter) {\n    case 'app':\n      createFilter = makeAppFilter(args.appId as string);\n      break;\n    case 'tag':\n      createFilter = makeTagsFilter(...(args.tags as string[]));\n      break;\n    case 'match':\n      createFilter = makeMatchFilter(\n        ...(args.regexes as string[]).map(\n          (value: string) => new RegExp(value, 'gm')\n        )\n      );\n      break;\n    case 'custom':\n      createFilter = makeCustomFilter(...(args.patterns as string[]));\n      break;\n    case 'all':\n    default:\n  }\n  const emitter = logkitty({\n    platform: platform as Platform,\n    adbPath: args.adbPath ? String(args.adbPath) : '',\n    priority:\n      platform === 'android'\n        ? getMinPriority(\n            AndroidPriority,\n            selectedAndroidPriorities,\n            AndroidPriority.DEBUG\n          )\n        : getMinPriority(\n            IosPriority,\n            selectedIosPriorities,\n            IosPriority.DEFAULT\n          ),\n    filter: createFilter,\n  });\n\n  emitter.on('entry', (entry: Entry) => {\n    process.stdout.write(formatEntry(entry));\n  });\n\n  emitter.on('error', (error: Error) => {\n    terminate(error);\n  });\n} catch (error) {\n  terminate(error as Error | CodeError);\n}\n\nfunction terminate(error: CodeError | Error) {\n  // eslint-disable-next-line no-console\n  console.log(formatError(error));\n  process.exit(1);\n}\n"], "file": "cli.js"}